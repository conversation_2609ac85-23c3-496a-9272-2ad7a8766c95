# Real-Time Object Detection for Smart Cameras

A comprehensive real-time object detection system designed for smart camera applications, featuring advanced computer vision capabilities for security monitoring, retail analytics, and intelligent surveillance. This project demonstrates the full lifecycle of a computer vision project, from dataset considerations and model training to optimization, real-time video processing, and deployment.

## 🎯 Project Overview

The Real-Time Object Detection for Smart Cameras system is engineered for applications like security monitoring and retail analytics. It can be deployed on edge devices or use standard webcams, providing efficient real-time video processing with optimized inference performance.

### Core Features

- **Object Detection**: Using YOLOv8 (or other configurable models) for accurate real-time detection
- **Real-time Video Processing**: Live processing from webcams or video files
- **Model Optimization**: Efficient inference with ONNX conversion and quantization
- **Modular Architecture**: Separate scripts for training, optimization, and detection
- **Containerized Deployment**: Docker support for easy deployment
- **Web Interface**: Modern React frontend for monitoring and configuration
- **Edge Device Support**: Optimized for deployment on smart cameras and edge devices

### Showcased Concepts

- **Object Detection Algorithms**: Implementation of YOLO architectures
- **Model Optimization Techniques**: ONNX conversion, quantization for efficient inference
- **Real-time Video Processing**: OpenCV integration for live camera feeds
- **System Integration**: Practical deployment considerations for smart camera systems
- **Performance Optimization**: Edge and server environment deployment strategies

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- Node.js 14 or higher
- CUDA-compatible GPU (recommended for training and faster inference)
- Webcam or IP camera for real-time detection

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Hasif50/real-time-object-detection.git
   cd real-time-object-detection
   ```

2. **Install backend dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install frontend dependencies**
   ```bash
   cd frontend
   npm install
   cd ..
   ```

4. **Download pre-trained models**
   ```bash
   python scripts/download_models.py
   ```

5. **Run the detection system**
   ```bash
   # Start backend server
   python real_time_detector.py

   # Start frontend (in a new terminal)
   cd frontend
   npm start
   ```

6. **Access the web interface**
   Open your browser and navigate to `http://localhost:3000`

## 📁 Project Structure

```
real-time-object-detection/
├── backend/                    # Backend API and core logic
│   ├── app/                   # Flask application
│   ├── detection/             # Object detection modules
│   ├── utils/                 # Utility functions
│   └── config/                # Configuration files
├── frontend/                  # React frontend application
│   ├── src/                   # Source code
│   ├── public/                # Static assets
│   └── build/                 # Production build
├── models/                    # Pre-trained model files
│   ├── yolo/                  # YOLO model variants
│   ├── optimized/             # Optimized models (ONNX, TensorRT)
│   └── custom/                # Custom trained models
├── data/                      # Datasets and sample data
│   ├── images/                # Sample images
│   ├── videos/                # Sample videos
│   └── annotations/           # Dataset annotations
├── tests/                     # Test suites
│   ├── unit/                  # Unit tests
│   └── integration/           # Integration tests
├── docs/                      # Documentation
│   ├── api/                   # API documentation
│   └── deployment/            # Deployment guides
├── scripts/                   # Utility scripts
├── requirements.txt           # Python dependencies
├── package.json              # Node.js dependencies
├── docker-compose.yml        # Docker configuration
├── .gitignore                # Git ignore rules
└── README.md                 # This file
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your-secret-key-here

# Model Configuration
MODEL_PATH=./models
DEFAULT_MODEL=yolov8n
CONFIDENCE_THRESHOLD=0.5
NMS_THRESHOLD=0.4

# Camera Configuration
CAMERA_INDEX=0
CAMERA_WIDTH=640
CAMERA_HEIGHT=480
FPS_TARGET=30

# Performance Configuration
USE_GPU=True
BATCH_SIZE=1
OPTIMIZATION_LEVEL=2
```

### Model Configuration

Configure detection settings in `backend/config/detection_config.yaml`:

```yaml
models:
  yolov8n:
    path: "models/yolo/yolov8n.pt"
    input_size: [640, 640]
    classes: 80  # COCO dataset classes
    confidence_threshold: 0.5
    nms_threshold: 0.4
  
  yolov8s:
    path: "models/yolo/yolov8s.pt"
    input_size: [640, 640]
    classes: 80
    confidence_threshold: 0.5
    nms_threshold: 0.4

camera:
  default_index: 0
  resolution: [640, 480]
  fps: 30
  buffer_size: 1

processing:
  use_gpu: true
  batch_processing: false
  optimization: "onnx"  # options: none, onnx, tensorrt
```

## 🎥 Supported Detection Models

### YOLO Models
- **YOLOv8n**: Nano version for edge devices and real-time applications
- **YOLOv8s**: Small version balancing speed and accuracy
- **YOLOv8m**: Medium version for higher accuracy requirements
- **YOLOv8l**: Large version for maximum accuracy
- **YOLOv8x**: Extra-large version for research and high-precision applications

### Custom Models
- **Security Camera Model**: Optimized for person and vehicle detection
- **Retail Analytics Model**: Focused on customer behavior and product interaction
- **Traffic Monitoring Model**: Specialized for vehicle and pedestrian detection

## 📊 API Documentation

### Start Detection Stream

```http
POST /api/detection/start
Content-Type: application/json

{
  "camera_index": 0,
  "model": "yolov8n",
  "confidence_threshold": 0.5,
  "save_output": false
}
```

### Get Detection Results

```http
GET /api/detection/results

Response:
{
  "status": "active",
  "fps": 28.5,
  "detections": [
    {
      "class": "person",
      "confidence": 0.92,
      "bbox": [100, 150, 200, 400],
      "timestamp": "2024-01-01T12:00:00Z"
    }
  ],
  "frame_count": 1250,
  "processing_time": 0.035
}
```

### Stop Detection Stream

```http
POST /api/detection/stop

Response:
{
  "status": "stopped",
  "total_frames": 1250,
  "average_fps": 28.5,
  "total_detections": 450
}
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
python -m pytest tests/

# Run specific test categories
python -m pytest tests/unit/
python -m pytest tests/integration/

# Run with coverage
python -m pytest tests/ --cov=backend --cov-report=html

# Test real-time detection
python tests/test_detection_pipeline.py
```

## 🐳 Docker Deployment

Deploy using Docker Compose:

```bash
# Build and start services
docker-compose up --build

# Run in production mode
docker-compose -f docker-compose.prod.yml up -d

# Scale detection workers
docker-compose up --scale detection-worker=3
```

## 📈 Performance Metrics

### Model Performance (COCO Dataset)
- **YOLOv8n**: 37.3 mAP@0.5:0.95, 80+ FPS on RTX 3080
- **YOLOv8s**: 44.9 mAP@0.5:0.95, 60+ FPS on RTX 3080
- **YOLOv8m**: 50.2 mAP@0.5:0.95, 45+ FPS on RTX 3080

### System Performance
- **Real-time Processing**: 30+ FPS on standard webcam input
- **Edge Device Performance**: 15+ FPS on Raspberry Pi 4 with optimization
- **Memory Usage**: <2GB RAM for standard models
- **Latency**: <50ms end-to-end processing time

## 🔧 Model Training and Optimization

### Training Custom Models

```bash
# Train on custom dataset
python train.py --data custom_dataset.yaml --model yolov8n.pt --epochs 100

# Resume training
python train.py --resume runs/train/exp/weights/last.pt

# Validate model
python optimize_model.py --model runs/train/exp/weights/best.pt --data custom_dataset.yaml
```

### Model Optimization

```bash
# Convert to ONNX
python optimize_model.py --model yolov8n.pt --format onnx

# Quantize model
python optimize_model.py --model yolov8n.pt --quantize int8

# TensorRT optimization (NVIDIA GPUs)
python optimize_model.py --model yolov8n.pt --format tensorrt
```

## 🌐 Web Interface Features

- **Live Video Stream**: Real-time camera feed with detection overlays
- **Detection Statistics**: Live metrics and performance monitoring
- **Model Management**: Switch between different detection models
- **Configuration Panel**: Adjust detection parameters in real-time
- **Recording Controls**: Save detection sessions and export results
- **Analytics Dashboard**: Historical data and detection trends

## 🔒 Security and Privacy

- **Local Processing**: All detection processing happens locally
- **No Data Transmission**: Video streams are not sent to external servers
- **Configurable Storage**: Optional local storage of detection results
- **Access Control**: Web interface authentication and authorization
- **Privacy Compliance**: GDPR and privacy regulation considerations

## 🤝 Contributing

We welcome contributions from Hasif's Workspace! Please read our contributing guidelines:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Developed from Hasif's Workspace
- YOLO models from Ultralytics
- OpenCV for computer vision operations
- React.js for the frontend interface
- Flask for the backend API

## 📞 Support

For support and questions, please contact:
- **Developer**: Hasif50
- **Email**: <EMAIL>
- **GitHub**: [@Hasif50](https://github.com/Hasif50)

## 🚀 Deployment Options

### Edge Devices
- **Raspberry Pi 4**: Optimized models for ARM processors
- **NVIDIA Jetson**: GPU-accelerated inference on edge
- **Intel NUC**: x86 edge computing solutions

### Cloud Deployment
- **AWS**: EC2 instances with GPU support
- **Google Cloud**: Compute Engine with TPU options
- **Azure**: Virtual machines with AI acceleration

### Smart Camera Integration
- **IP Camera Support**: RTSP stream processing
- **USB Camera**: Direct USB camera integration
- **Network Cameras**: Multi-camera system support

---

**Note**: This system is designed for educational and research purposes. For production deployment in security or commercial applications, ensure compliance with local regulations and privacy laws.
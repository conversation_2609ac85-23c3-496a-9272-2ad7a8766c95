"""
Real-Time Object Detection for Smart Cameras - Object Detector
Developed from Hasif's Workspace

This module contains the main ObjectDetector class that handles model loading,
inference, and object detection for real-time video streams.
"""

import cv2
import time
import logging
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union

import torch
from ultralytics import YOLO
import onnxruntime as ort

logger = logging.getLogger(__name__)


class ObjectDetector:
    """
    Main object detection class supporting multiple model formats and optimizations.
    """

    def __init__(self, config: Dict):
        """
        Initialize the object detector.

        Args:
            config: Configuration dictionary containing model and detection settings
        """
        self.config = config
        self.model = None
        self.model_type = None
        self.current_model_name = None
        self.class_names = []
        self.input_size = (640, 640)
        self.confidence_threshold = config.get("detection", {}).get(
            "confidence_threshold", 0.5
        )
        self.nms_threshold = config.get("detection", {}).get("nms_threshold", 0.4)

        # Performance tracking
        self.inference_times = []
        self.total_detections = 0

        # Device configuration
        self.device = self._setup_device()

        logger.info(f"ObjectDetector initialized with device: {self.device}")

    def _setup_device(self) -> str:
        """Setup computation device (CPU/GPU)."""
        try:
            if torch.cuda.is_available():
                device = "cuda"
                logger.info(f"CUDA available: {torch.cuda.get_device_name(0)}")
            else:
                device = "cpu"
                logger.info("Using CPU for inference")
            return device
        except Exception as e:
            logger.warning(f"Error setting up device: {e}, falling back to CPU")
            return "cpu"

    def load_model(self, model_name: str) -> bool:
        """
        Load a detection model.

        Args:
            model_name: Name of the model to load

        Returns:
            True if model loaded successfully, False otherwise
        """
        try:
            model_config = self.config.get("models", {}).get(model_name)
            if not model_config:
                logger.error(f"Model configuration not found for: {model_name}")
                return False

            model_path = model_config.get("path")
            if not model_path or not Path(model_path).exists():
                logger.error(f"Model file not found: {model_path}")
                return False

            # Determine model type from file extension
            model_path = Path(model_path)
            if model_path.suffix == ".pt":
                self.model_type = "pytorch"
                self.model = YOLO(str(model_path))
                if self.device == "cuda":
                    self.model.to("cuda")
            elif model_path.suffix == ".onnx":
                self.model_type = "onnx"
                providers = (
                    ["CUDAExecutionProvider", "CPUExecutionProvider"]
                    if self.device == "cuda"
                    else ["CPUExecutionProvider"]
                )
                self.model = ort.InferenceSession(str(model_path), providers=providers)
            else:
                logger.error(f"Unsupported model format: {model_path.suffix}")
                return False

            # Set model properties
            self.current_model_name = model_name
            self.input_size = tuple(model_config.get("input_size", [640, 640]))
            self.class_names = model_config.get("class_names", self._get_coco_classes())

            logger.info(f"Model loaded successfully: {model_name} ({self.model_type})")
            logger.info(
                f"Input size: {self.input_size}, Classes: {len(self.class_names)}"
            )

            return True

        except Exception as e:
            logger.error(f"Error loading model {model_name}: {str(e)}")
            return False

    def detect(self, frame: np.ndarray) -> List[Dict]:
        """
        Perform object detection on a frame.

        Args:
            frame: Input image frame as numpy array

        Returns:
            List of detection dictionaries containing class, confidence, and bbox
        """
        if self.model is None:
            logger.warning("No model loaded for detection")
            return []

        try:
            start_time = time.time()

            if self.model_type == "pytorch":
                detections = self._detect_pytorch(frame)
            elif self.model_type == "onnx":
                detections = self._detect_onnx(frame)
            else:
                logger.error(f"Unsupported model type: {self.model_type}")
                return []

            # Track performance
            inference_time = time.time() - start_time
            self.inference_times.append(inference_time)
            if len(self.inference_times) > 100:  # Keep last 100 measurements
                self.inference_times.pop(0)

            self.total_detections += len(detections)

            return detections

        except Exception as e:
            logger.error(f"Error during detection: {str(e)}")
            return []

    def _detect_pytorch(self, frame: np.ndarray) -> List[Dict]:
        """Perform detection using PyTorch YOLO model."""
        try:
            # Run inference
            results = self.model(
                frame, conf=self.confidence_threshold, iou=self.nms_threshold
            )

            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Extract detection data
                        xyxy = box.xyxy[0].cpu().numpy()
                        conf = float(box.conf[0].cpu().numpy())
                        cls = int(box.cls[0].cpu().numpy())

                        # Convert to detection format
                        detection = {
                            "class_id": cls,
                            "class_name": self.class_names[cls]
                            if cls < len(self.class_names)
                            else f"class_{cls}",
                            "confidence": conf,
                            "bbox": {
                                "x": float(xyxy[0]),
                                "y": float(xyxy[1]),
                                "width": float(xyxy[2] - xyxy[0]),
                                "height": float(xyxy[3] - xyxy[1]),
                            },
                        }
                        detections.append(detection)

            return detections

        except Exception as e:
            logger.error(f"Error in PyTorch detection: {str(e)}")
            return []

    def _detect_onnx(self, frame: np.ndarray) -> List[Dict]:
        """Perform detection using ONNX model."""
        try:
            # Preprocess frame
            input_tensor = self._preprocess_frame(frame)

            # Run inference
            input_name = self.model.get_inputs()[0].name
            outputs = self.model.run(None, {input_name: input_tensor})

            # Post-process outputs
            detections = self._postprocess_onnx_outputs(outputs[0], frame.shape)

            return detections

        except Exception as e:
            logger.error(f"Error in ONNX detection: {str(e)}")
            return []

    def _preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """Preprocess frame for ONNX model input."""
        try:
            # Resize frame to model input size
            resized = cv2.resize(frame, self.input_size)

            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)

            # Normalize to [0, 1]
            normalized = rgb_frame.astype(np.float32) / 255.0

            # Transpose to CHW format and add batch dimension
            input_tensor = np.transpose(normalized, (2, 0, 1))
            input_tensor = np.expand_dims(input_tensor, axis=0)

            return input_tensor

        except Exception as e:
            logger.error(f"Error preprocessing frame: {str(e)}")
            return np.array([])

    def _postprocess_onnx_outputs(
        self, outputs: np.ndarray, original_shape: Tuple[int, int, int]
    ) -> List[Dict]:
        """Post-process ONNX model outputs to extract detections."""
        try:
            detections = []

            # Assuming YOLO output format: [batch, num_detections, 85] where 85 = 4 bbox + 1 conf + 80 classes
            if len(outputs.shape) == 3:
                outputs = outputs[0]  # Remove batch dimension

            # Filter by confidence threshold
            confidences = outputs[:, 4]
            valid_indices = confidences > self.confidence_threshold
            valid_outputs = outputs[valid_indices]

            if len(valid_outputs) == 0:
                return detections

            # Extract class scores and get best class for each detection
            class_scores = valid_outputs[:, 5:]
            class_ids = np.argmax(class_scores, axis=1)
            class_confidences = np.max(class_scores, axis=1)

            # Calculate final confidence scores
            final_confidences = valid_outputs[:, 4] * class_confidences

            # Extract bounding boxes
            boxes = valid_outputs[:, :4]

            # Convert from center format to corner format and scale to original image
            h, w = original_shape[:2]
            scale_x = w / self.input_size[0]
            scale_y = h / self.input_size[1]

            for i, (box, conf, cls_id) in enumerate(
                zip(boxes, final_confidences, class_ids)
            ):
                if conf > self.confidence_threshold:
                    # Convert center format to corner format
                    center_x, center_y, width, height = box
                    x1 = (center_x - width / 2) * scale_x
                    y1 = (center_y - height / 2) * scale_y
                    x2 = (center_x + width / 2) * scale_x
                    y2 = (center_y + height / 2) * scale_y

                    detection = {
                        "class_id": int(cls_id),
                        "class_name": self.class_names[cls_id]
                        if cls_id < len(self.class_names)
                        else f"class_{cls_id}",
                        "confidence": float(conf),
                        "bbox": {
                            "x": float(x1),
                            "y": float(y1),
                            "width": float(x2 - x1),
                            "height": float(y2 - y1),
                        },
                    }
                    detections.append(detection)

            # Apply Non-Maximum Suppression
            detections = self._apply_nms(detections)

            return detections

        except Exception as e:
            logger.error(f"Error post-processing ONNX outputs: {str(e)}")
            return []

    def _apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """Apply Non-Maximum Suppression to remove duplicate detections."""
        if len(detections) == 0:
            return detections

        try:
            # Convert to format expected by cv2.dnn.NMSBoxes
            boxes = []
            confidences = []

            for det in detections:
                bbox = det["bbox"]
                boxes.append([bbox["x"], bbox["y"], bbox["width"], bbox["height"]])
                confidences.append(det["confidence"])

            # Apply NMS
            indices = cv2.dnn.NMSBoxes(
                boxes, confidences, self.confidence_threshold, self.nms_threshold
            )

            if len(indices) > 0:
                indices = indices.flatten()
                return [detections[i] for i in indices]
            else:
                return []

        except Exception as e:
            logger.error(f"Error applying NMS: {str(e)}")
            return detections

    def _get_coco_classes(self) -> List[str]:
        """Get COCO dataset class names."""
        return [
            "person",
            "bicycle",
            "car",
            "motorcycle",
            "airplane",
            "bus",
            "train",
            "truck",
            "boat",
            "traffic light",
            "fire hydrant",
            "stop sign",
            "parking meter",
            "bench",
            "bird",
            "cat",
            "dog",
            "horse",
            "sheep",
            "cow",
            "elephant",
            "bear",
            "zebra",
            "giraffe",
            "backpack",
            "umbrella",
            "handbag",
            "tie",
            "suitcase",
            "frisbee",
            "skis",
            "snowboard",
            "sports ball",
            "kite",
            "baseball bat",
            "baseball glove",
            "skateboard",
            "surfboard",
            "tennis racket",
            "bottle",
            "wine glass",
            "cup",
            "fork",
            "knife",
            "spoon",
            "bowl",
            "banana",
            "apple",
            "sandwich",
            "orange",
            "broccoli",
            "carrot",
            "hot dog",
            "pizza",
            "donut",
            "cake",
            "chair",
            "couch",
            "potted plant",
            "bed",
            "dining table",
            "toilet",
            "tv",
            "laptop",
            "mouse",
            "remote",
            "keyboard",
            "cell phone",
            "microwave",
            "oven",
            "toaster",
            "sink",
            "refrigerator",
            "book",
            "clock",
            "vase",
            "scissors",
            "teddy bear",
            "hair drier",
            "toothbrush",
        ]

    def get_performance_stats(self) -> Dict:
        """Get performance statistics."""
        if not self.inference_times:
            return {
                "average_inference_time": 0.0,
                "fps": 0.0,
                "total_detections": self.total_detections,
            }

        avg_inference_time = np.mean(self.inference_times)
        fps = 1.0 / avg_inference_time if avg_inference_time > 0 else 0.0

        return {
            "average_inference_time": avg_inference_time,
            "fps": fps,
            "total_detections": self.total_detections,
            "model_name": self.current_model_name,
            "model_type": self.model_type,
            "device": self.device,
        }

    def update_thresholds(
        self, confidence_threshold: float = None, nms_threshold: float = None
    ):
        """Update detection thresholds."""
        if confidence_threshold is not None:
            self.confidence_threshold = confidence_threshold
        if nms_threshold is not None:
            self.nms_threshold = nms_threshold

        logger.info(
            f"Updated thresholds - Confidence: {self.confidence_threshold}, NMS: {self.nms_threshold}"
        )

    def reset_stats(self):
        """Reset performance statistics."""
        self.inference_times = []
        self.total_detections = 0
        logger.info("Performance statistics reset")

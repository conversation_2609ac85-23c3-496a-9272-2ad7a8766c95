# Real-Time Object Detection for Smart Cameras - Docker Compose
# Developed from <PERSON><PERSON>'s Workspace

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: realtime_detection_db
    environment:
      POSTGRES_DB: detection_system
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: detection_password_2024
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    ports:
      - "5432:5432"
    networks:
      - detection_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: realtime_detection_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - detection_network
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: realtime_detection_backend
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=***********************************************************/detection_system
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=realtime-detection-secret-key-2024
      - MODEL_PATH=/app/models
      - UPLOAD_FOLDER=/app/uploads
      - USE_GPU=false
      - CAMERA_INDEX=0
    volumes:
      - ./models:/app/models
      - ./data/uploads:/app/uploads
      - ./data/output:/app/output
      - ./logs:/app/logs
      - /dev/video0:/dev/video0  # Camera access (Linux)
    ports:
      - "5000:5000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - detection_network
    restart: unless-stopped
    privileged: true  # Required for camera access
    devices:
      - /dev/video0:/dev/video0  # Camera device
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: realtime_detection_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:5000
      - REACT_APP_VERSION=1.0.0
      - REACT_APP_ENVIRONMENT=production
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - detection_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: realtime_detection_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    networks:
      - detection_network
    restart: unless-stopped

  # Model Download Service (runs once to download models)
  model_downloader:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: realtime_detection_models
    environment:
      - PYTHONPATH=/app
    volumes:
      - ./models:/app/models
    command: python scripts/download_models.py --all
    networks:
      - detection_network
    restart: "no"  # Run once only

  # Performance Monitor (optional)
  monitor:
    image: prom/prometheus:latest
    container_name: realtime_detection_monitor
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - detection_network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana Dashboard (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: realtime_detection_grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - detection_network
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  detection_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Development override
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
---
# docker-compose.dev.yml
version: '3.8'

services:
  backend:
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=True
      - USE_GPU=false
    volumes:
      - .:/app
    command: python real_time_detector.py --web --host 0.0.0.0

  frontend:
    environment:
      - REACT_APP_API_URL=http://localhost:5000
      - REACT_APP_ENVIRONMENT=development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm start

# Production override
# Use: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up
---
# docker-compose.prod.yml
version: '3.8'

services:
  backend:
    environment:
      - FLASK_ENV=production
      - USE_GPU=true
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

  frontend:
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Load balancer for production
  haproxy:
    image: haproxy:alpine
    container_name: realtime_detection_lb
    ports:
      - "80:80"
      - "443:443"
      - "8404:8404"  # Stats page
    volumes:
      - ./haproxy/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg
      - ./ssl:/etc/ssl/certs
    depends_on:
      - backend
      - frontend
    networks:
      - detection_network
    restart: unless-stopped

# GPU-enabled override (requires nvidia-docker)
# Use: docker-compose -f docker-compose.yml -f docker-compose.gpu.yml up
---
# docker-compose.gpu.yml
version: '3.8'

services:
  backend:
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - USE_GPU=true
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
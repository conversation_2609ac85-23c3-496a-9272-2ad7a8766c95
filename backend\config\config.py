"""
Real-Time Object Detection for Smart Cameras - Configuration
Developed from <PERSON><PERSON>'s Workspace

This module contains configuration classes for different environments
(development, testing, production) for the real-time object detection system.
"""

import os
from pathlib import Path


class Config:
    """Base configuration class."""

    # Flask settings
    SECRET_KEY = os.environ.get("SECRET_KEY") or "real-time-detection-secret-key-2024"

    # Database settings
    SQLALCHEMY_DATABASE_URI = (
        os.environ.get("DATABASE_URL") or "sqlite:///detection_system.db"
    )
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True

    # File upload settings
    UPLOAD_FOLDER = os.environ.get("UPLOAD_FOLDER") or "data/uploads"
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB max file size

    # Model settings
    MODEL_PATH = os.environ.get("MODEL_PATH") or "models"
    DEFAULT_MODEL = os.environ.get("DEFAULT_MODEL") or "yolov8n"

    # Detection settings
    CONFIDENCE_THRESHOLD = float(os.environ.get("CONFIDENCE_THRESHOLD", "0.5"))
    NMS_THRESHOLD = float(os.environ.get("NMS_THRESHOLD", "0.4"))

    # Camera settings
    CAMERA_INDEX = int(os.environ.get("CAMERA_INDEX", "0"))
    CAMERA_WIDTH = int(os.environ.get("CAMERA_WIDTH", "640"))
    CAMERA_HEIGHT = int(os.environ.get("CAMERA_HEIGHT", "480"))
    FPS_TARGET = int(os.environ.get("FPS_TARGET", "30"))

    # Performance settings
    USE_GPU = os.environ.get("USE_GPU", "True").lower() == "true"
    BATCH_SIZE = int(os.environ.get("BATCH_SIZE", "1"))
    OPTIMIZATION_LEVEL = int(os.environ.get("OPTIMIZATION_LEVEL", "2"))

    # Logging settings
    LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")
    LOG_FILE = os.environ.get("LOG_FILE", "logs/detection_system.log")

    # Security settings
    CORS_ORIGINS = os.environ.get("CORS_ORIGINS", "*").split(",")

    # Session settings
    SESSION_TIMEOUT = int(os.environ.get("SESSION_TIMEOUT", "3600"))  # 1 hour

    @staticmethod
    def init_app(app):
        """Initialize application with configuration."""
        # Create necessary directories
        directories = [
            Config.UPLOAD_FOLDER,
            Config.MODEL_PATH,
            "logs",
            "data/output",
            "data/cache",
        ]

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)


class DevelopmentConfig(Config):
    """Development configuration."""

    DEBUG = True
    TESTING = False

    # Development database
    SQLALCHEMY_DATABASE_URI = (
        os.environ.get("DEV_DATABASE_URL") or "sqlite:///detection_system_dev.db"
    )

    # Relaxed security for development
    SQLALCHEMY_ECHO = True

    # Development-specific settings
    CAMERA_INDEX = 0
    USE_GPU = False  # Use CPU for development to avoid GPU conflicts
    LOG_LEVEL = "DEBUG"


class TestingConfig(Config):
    """Testing configuration."""

    DEBUG = False
    TESTING = True

    # In-memory database for testing
    SQLALCHEMY_DATABASE_URI = "sqlite:///:memory:"

    # Disable CSRF for testing
    WTF_CSRF_ENABLED = False

    # Testing-specific settings
    CAMERA_INDEX = -1  # Disable camera for testing
    USE_GPU = False
    LOG_LEVEL = "WARNING"


class ProductionConfig(Config):
    """Production configuration."""

    DEBUG = False
    TESTING = False

    # Production database
    SQLALCHEMY_DATABASE_URI = (
        os.environ.get("DATABASE_URL")
        or "postgresql://user:password@localhost/detection_system"
    )

    # Production security settings
    SECRET_KEY = os.environ.get("SECRET_KEY")
    if not SECRET_KEY:
        raise ValueError("No SECRET_KEY set for production environment")

    # Production-specific settings
    USE_GPU = True
    LOG_LEVEL = "INFO"

    # Performance optimizations
    SQLALCHEMY_ENGINE_OPTIONS = {
        "pool_pre_ping": True,
        "pool_recycle": 300,
    }

    @classmethod
    def init_app(cls, app):
        """Initialize production application."""
        Config.init_app(app)

        # Log to syslog in production
        import logging
        from logging.handlers import SysLogHandler

        syslog_handler = SysLogHandler()
        syslog_handler.setLevel(logging.WARNING)
        app.logger.addHandler(syslog_handler)


class DockerConfig(ProductionConfig):
    """Docker container configuration."""

    # Docker-specific database URL
    SQLALCHEMY_DATABASE_URI = (
        os.environ.get("DATABASE_URL")
        or "**************************************/detection_system"
    )

    # Docker networking
    CORS_ORIGINS = ["http://localhost:3000", "http://frontend:3000"]


# Configuration mapping
config = {
    "development": DevelopmentConfig,
    "testing": TestingConfig,
    "production": ProductionConfig,
    "docker": DockerConfig,
    "default": DevelopmentConfig,
}


def get_config(config_name=None):
    """Get configuration class by name."""
    config_name = config_name or os.environ.get("FLASK_ENV", "default")
    return config.get(config_name, DevelopmentConfig)

#!/usr/bin/env python3
"""
Real-Time Object Detection for Smart Cameras - Model Download Script
Developed from Has<PERSON>'s Workspace

This script downloads and sets up pre-trained YOLO models for real-time object detection.
"""

import os
import sys
import requests
import hashlib
import logging
from pathlib import Path
from typing import Dict, List, Optional
from urllib.parse import urlparse

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Model download URLs and checksums
YOLO_MODELS = {
    "yolov8n": {
        "url": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt",
        "filename": "yolov8n.pt",
        "size_mb": 6.2,
        "description": "YOLOv8 Nano - Fastest model for real-time applications",
    },
    "yolov8s": {
        "url": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt",
        "filename": "yolov8s.pt",
        "size_mb": 21.5,
        "description": "YOLOv8 Small - Balanced speed and accuracy",
    },
    "yolov8m": {
        "url": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8m.pt",
        "filename": "yolov8m.pt",
        "size_mb": 49.7,
        "description": "YOLOv8 Medium - Higher accuracy, moderate speed",
    },
    "yolov8l": {
        "url": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8l.pt",
        "filename": "yolov8l.pt",
        "size_mb": 83.7,
        "description": "YOLOv8 Large - High accuracy, slower inference",
    },
    "yolov8x": {
        "url": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8x.pt",
        "filename": "yolov8x.pt",
        "size_mb": 136.7,
        "description": "YOLOv8 Extra Large - Highest accuracy, slowest inference",
    },
}

# Additional specialized models
SPECIALIZED_MODELS = {
    "yolov8n-seg": {
        "url": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n-seg.pt",
        "filename": "yolov8n-seg.pt",
        "size_mb": 6.7,
        "description": "YOLOv8 Nano Segmentation - Instance segmentation",
    },
    "yolov8s-seg": {
        "url": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s-seg.pt",
        "filename": "yolov8s-seg.pt",
        "size_mb": 23.8,
        "description": "YOLOv8 Small Segmentation - Instance segmentation",
    },
}


class ModelDownloader:
    """Handles downloading and managing YOLO models."""

    def __init__(self, models_dir: str = "models"):
        """
        Initialize the model downloader.

        Args:
            models_dir: Directory to store downloaded models
        """
        self.models_dir = Path(models_dir)
        self.yolo_dir = self.models_dir / "yolo"
        self.custom_dir = self.models_dir / "custom"
        self.optimized_dir = self.models_dir / "optimized"

        # Create directories
        self._create_directories()

    def _create_directories(self):
        """Create necessary directories for models."""
        directories = [
            self.models_dir,
            self.yolo_dir,
            self.custom_dir,
            self.optimized_dir,
            self.models_dir / "weights",
            self.models_dir / "checkpoints",
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"✓ Created directory: {directory}")

    def download_model(
        self, model_name: str, model_info: Dict, force: bool = False
    ) -> bool:
        """
        Download a single model.

        Args:
            model_name: Name of the model
            model_info: Model information dictionary
            force: Force download even if file exists

        Returns:
            True if download successful, False otherwise
        """
        try:
            filename = model_info["filename"]
            url = model_info["url"]
            size_mb = model_info["size_mb"]
            description = model_info["description"]

            # Determine output path
            if "seg" in model_name:
                output_path = self.yolo_dir / filename
            else:
                output_path = self.yolo_dir / filename

            # Check if file already exists
            if output_path.exists() and not force:
                logger.info(f"✓ Model already exists: {filename}")
                return True

            logger.info(f"📥 Downloading {model_name}: {description}")
            logger.info(f"   Size: {size_mb} MB")
            logger.info(f"   URL: {url}")

            # Download with progress
            success = self._download_with_progress(url, output_path)

            if success:
                logger.info(f"✅ Successfully downloaded: {filename}")
                return True
            else:
                logger.error(f"❌ Failed to download: {filename}")
                return False

        except Exception as e:
            logger.error(f"❌ Error downloading {model_name}: {str(e)}")
            return False

    def _download_with_progress(self, url: str, output_path: Path) -> bool:
        """
        Download file with progress indication.

        Args:
            url: URL to download from
            output_path: Path to save the file

        Returns:
            True if download successful, False otherwise
        """
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()

            total_size = int(response.headers.get("content-length", 0))
            downloaded_size = 0

            with open(output_path, "wb") as file:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        file.write(chunk)
                        downloaded_size += len(chunk)

                        # Show progress
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(
                                f"\r   Progress: {progress:.1f}% ({downloaded_size / 1024 / 1024:.1f} MB)",
                                end="",
                            )

            print()  # New line after progress
            return True

        except requests.RequestException as e:
            logger.error(f"Download error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during download: {str(e)}")
            return False

    def download_all_models(
        self, include_specialized: bool = False, force: bool = False
    ) -> Dict[str, bool]:
        """
        Download all available models.

        Args:
            include_specialized: Whether to include specialized models
            force: Force download even if files exist

        Returns:
            Dictionary with download results for each model
        """
        results = {}

        # Download standard YOLO models
        logger.info("🔄 Downloading standard YOLO models...")
        for model_name, model_info in YOLO_MODELS.items():
            results[model_name] = self.download_model(model_name, model_info, force)

        # Download specialized models if requested
        if include_specialized:
            logger.info("🔄 Downloading specialized models...")
            for model_name, model_info in SPECIALIZED_MODELS.items():
                results[model_name] = self.download_model(model_name, model_info, force)

        return results

    def download_specific_models(
        self, model_names: List[str], force: bool = False
    ) -> Dict[str, bool]:
        """
        Download specific models by name.

        Args:
            model_names: List of model names to download
            force: Force download even if files exist

        Returns:
            Dictionary with download results for each model
        """
        results = {}
        all_models = {**YOLO_MODELS, **SPECIALIZED_MODELS}

        for model_name in model_names:
            if model_name in all_models:
                results[model_name] = self.download_model(
                    model_name, all_models[model_name], force
                )
            else:
                logger.error(f"❌ Unknown model: {model_name}")
                results[model_name] = False

        return results

    def list_available_models(self) -> Dict[str, Dict]:
        """
        List all available models for download.

        Returns:
            Dictionary of available models
        """
        return {**YOLO_MODELS, **SPECIALIZED_MODELS}

    def list_downloaded_models(self) -> List[str]:
        """
        List models that have been downloaded.

        Returns:
            List of downloaded model filenames
        """
        downloaded = []

        # Check YOLO directory
        if self.yolo_dir.exists():
            for model_file in self.yolo_dir.glob("*.pt"):
                downloaded.append(model_file.name)

        # Check custom directory
        if self.custom_dir.exists():
            for model_file in self.custom_dir.glob("*.pt"):
                downloaded.append(f"custom/{model_file.name}")

        return downloaded

    def verify_model(self, model_path: Path) -> bool:
        """
        Verify that a model file is valid.

        Args:
            model_path: Path to the model file

        Returns:
            True if model is valid, False otherwise
        """
        try:
            if not model_path.exists():
                return False

            # Check file size (should be > 1MB for valid models)
            if model_path.stat().st_size < 1024 * 1024:
                return False

            # Try to load with ultralytics (if available)
            try:
                from ultralytics import YOLO

                model = YOLO(str(model_path))
                return True
            except ImportError:
                logger.warning("Ultralytics not available for model verification")
                return True  # Assume valid if we can't verify
            except Exception:
                return False

        except Exception as e:
            logger.error(f"Error verifying model {model_path}: {str(e)}")
            return False

    def create_model_configs(self):
        """Create model configuration files."""
        configs = {
            "yolov8n": {
                "display_name": "YOLOv8 Nano",
                "description": "Fastest model, optimized for real-time applications",
                "input_size": [640, 640],
                "classes": 80,
                "confidence_threshold": 0.5,
                "nms_threshold": 0.4,
                "performance": {
                    "map50_95": 37.3,
                    "speed_cpu": 80.4,
                    "speed_gpu": 0.99,
                    "params_m": 3.2,
                    "flops_b": 8.7,
                },
            },
            "yolov8s": {
                "display_name": "YOLOv8 Small",
                "description": "Balanced speed and accuracy",
                "input_size": [640, 640],
                "classes": 80,
                "confidence_threshold": 0.5,
                "nms_threshold": 0.4,
                "performance": {
                    "map50_95": 44.9,
                    "speed_cpu": 128.1,
                    "speed_gpu": 1.20,
                    "params_m": 11.2,
                    "flops_b": 28.6,
                },
            },
            "yolov8m": {
                "display_name": "YOLOv8 Medium",
                "description": "Higher accuracy, moderate speed",
                "input_size": [640, 640],
                "classes": 80,
                "confidence_threshold": 0.5,
                "nms_threshold": 0.4,
                "performance": {
                    "map50_95": 50.2,
                    "speed_cpu": 234.7,
                    "speed_gpu": 1.83,
                    "params_m": 25.9,
                    "flops_b": 78.9,
                },
            },
        }

        for model_name, config in configs.items():
            config_path = self.yolo_dir / f"{model_name}_config.yaml"

            try:
                import yaml

                with open(config_path, "w") as f:
                    yaml.dump(config, f, default_flow_style=False, indent=2)
                logger.info(f"✓ Created config: {config_path}")
            except ImportError:
                logger.warning("PyYAML not available, skipping config creation")
                break
            except Exception as e:
                logger.error(f"Error creating config for {model_name}: {str(e)}")


def main():
    """Main function for command-line interface."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Download YOLO models for real-time object detection"
    )
    parser.add_argument("--models", nargs="+", help="Specific models to download")
    parser.add_argument(
        "--all", action="store_true", help="Download all standard models"
    )
    parser.add_argument(
        "--specialized", action="store_true", help="Include specialized models"
    )
    parser.add_argument(
        "--force", action="store_true", help="Force download even if files exist"
    )
    parser.add_argument("--list", action="store_true", help="List available models")
    parser.add_argument(
        "--verify", action="store_true", help="Verify downloaded models"
    )
    parser.add_argument(
        "--models-dir", default="models", help="Models directory (default: models)"
    )

    args = parser.parse_args()

    print("🎥 Real-Time Object Detection for Smart Cameras - Model Downloader")
    print("   Developed from Hasif's Workspace")
    print("=" * 70)

    downloader = ModelDownloader(args.models_dir)

    try:
        if args.list:
            # List available models
            print("\n📋 Available Models:")
            all_models = downloader.list_available_models()
            for model_name, model_info in all_models.items():
                print(
                    f"  • {model_name}: {model_info['description']} ({model_info['size_mb']} MB)"
                )

            print("\n📦 Downloaded Models:")
            downloaded = downloader.list_downloaded_models()
            if downloaded:
                for model in downloaded:
                    print(f"  • {model}")
            else:
                print("  No models downloaded yet")

        elif args.verify:
            # Verify downloaded models
            print("\n🔍 Verifying downloaded models...")
            downloaded = downloader.list_downloaded_models()
            for model in downloaded:
                model_path = (
                    downloader.yolo_dir / model
                    if not model.startswith("custom/")
                    else downloader.custom_dir / model.replace("custom/", "")
                )
                if downloader.verify_model(model_path):
                    print(f"  ✅ {model}: Valid")
                else:
                    print(f"  ❌ {model}: Invalid or corrupted")

        elif args.models:
            # Download specific models
            print(f"\n📥 Downloading specific models: {', '.join(args.models)}")
            results = downloader.download_specific_models(args.models, args.force)

            # Show results
            print("\n📊 Download Results:")
            for model_name, success in results.items():
                status = "✅ Success" if success else "❌ Failed"
                print(f"  • {model_name}: {status}")

        elif args.all:
            # Download all models
            print("\n📥 Downloading all models...")
            results = downloader.download_all_models(args.specialized, args.force)

            # Show results
            print("\n📊 Download Results:")
            successful = sum(1 for success in results.values() if success)
            total = len(results)

            for model_name, success in results.items():
                status = "✅ Success" if success else "❌ Failed"
                print(f"  • {model_name}: {status}")

            print(f"\n📈 Summary: {successful}/{total} models downloaded successfully")

        else:
            # Default: download essential models
            print("\n📥 Downloading essential models (yolov8n, yolov8s)...")
            essential_models = ["yolov8n", "yolov8s"]
            results = downloader.download_specific_models(essential_models, args.force)

            # Show results
            print("\n📊 Download Results:")
            for model_name, success in results.items():
                status = "✅ Success" if success else "❌ Failed"
                print(f"  • {model_name}: {status}")

        # Create model configurations
        print("\n⚙️ Creating model configurations...")
        downloader.create_model_configs()

        print("\n✅ Model setup completed!")
        print("\nNext steps:")
        print("1. Run 'python real_time_detector.py' to start detection")
        print("2. Or run 'python real_time_detector.py --web' for web interface")
        print("3. Use 'python real_time_detector.py --help' for more options")

        return 0

    except KeyboardInterrupt:
        print("\n\n⚠️ Download interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"❌ Error: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())

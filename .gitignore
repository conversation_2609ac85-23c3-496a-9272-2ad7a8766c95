# Real-Time Object Detection for Smart Cameras - Git Ignore
# Developed from <PERSON><PERSON>'s Workspace

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# React build output
frontend/build/
frontend/dist/

# Package lock files (keep yarn.lock, ignore package-lock.json)
package-lock.json

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Model files (large binary files)
*.pt
*.pth
*.onnx
*.trt
*.engine
*.pb
*.h5
*.hdf5
*.pkl
*.pickle

# Model directories
models/yolo/
models/custom/
models/optimized/
models/weights/
models/checkpoints/

# Data directories
data/uploads/
data/output/
data/cache/
data/temp/
data/raw/
data/processed/

# Log files
logs/
*.log

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config/secrets.yaml
config/production.yaml
.env.local
.env.production

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Video and image files
*.mp4
*.avi
*.mov
*.mkv
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.webp

# Except sample/demo files
!data/samples/
!docs/images/
!frontend/public/

# Docker files
.dockerignore

# Jupyter notebook checkpoints
.ipynb_checkpoints/

# pytest
.pytest_cache/

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Profiling data
*.prof

# Memory profiling
*.mprof

# Performance monitoring data
performance_data/
metrics/

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# CUDA cache
.nv/

# TensorRT cache
*.cache

# OpenCV cache
.opencv/

# Matplotlib cache
.matplotlib/

# Weights & Biases
wandb/

# MLflow
mlruns/

# TensorBoard logs
runs/
tensorboard_logs/

# Experiment tracking
experiments/
results/

# Annotation files (large datasets)
annotations/
labels/

# Video processing temp files
*.yuv
*.raw

# Audio files (if any)
*.wav
*.mp3
*.flac

# Documentation build
docs/build/
docs/_build/

# Sphinx
docs/source/_build/

# Local development
.local/
local/

# Test outputs
test_outputs/
test_results/

# Benchmark results
benchmarks/
benchmark_results/

# Deployment files
deployment/secrets/
deployment/keys/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# API keys and secrets
api_keys.txt
secrets.txt
credentials.json

# Local configuration overrides
local_config.yaml
dev_config.yaml

# Editor backup files
*~
.#*
\#*#

# Emacs
.emacs.d/

# Vim
.vim/
*.swp
*.swo

# Sublime Text
*.sublime-project
*.sublime-workspace

# PyCharm
.idea/

# Visual Studio Code
.vscode/
*.code-workspace

# Atom
.atom/

# JetBrains
.idea/
*.iml
*.ipr
*.iws

# Eclipse
.project
.metadata
.classpath
.settings/

# NetBeans
nbproject/

# Android Studio
.gradle/
local.properties

# Xcode
*.xcodeproj/
*.xcworkspace/

# Windows
desktop.ini

# Linux
.directory

# macOS
.AppleDouble
.LSOverride
Icon?

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Real-time detection specific
detection_sessions/
camera_calibration/
roi_configs/
alert_logs/

# Performance logs
performance_logs/
system_metrics/

# User uploads
user_uploads/
session_data/

# Processed videos
processed_videos/
detection_videos/

# Analytics data
analytics/
statistics/

# Cache directories
.cache/
cache/

# Temporary processing
processing/
temp_processing/

# Model training artifacts
training_logs/
validation_results/
training_checkpoints/

# Hyperparameter tuning
hyperopt_results/
optuna_studies/

# Data augmentation cache
augmentation_cache/

# Feature extraction cache
features_cache/

# Inference cache
inference_cache/

# Visualization outputs
visualizations/
plots/
charts/

# Export files
exports/
reports/

# Backup configurations
config_backup/
settings_backup/
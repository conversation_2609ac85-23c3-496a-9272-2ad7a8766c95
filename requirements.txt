# Core Framework Dependencies
Flask==2.3.3
Flask-CORS==4.0.0
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-JWT-Extended==4.5.3

# Computer Vision and Object Detection
ultralytics==8.0.196
opencv-python==********
opencv-contrib-python==********
Pillow==10.0.1
numpy==1.24.3
scipy==1.11.3

# Deep Learning Frameworks
torch==2.0.1
torchvision==0.15.2
tensorflow==2.13.0
onnx==1.14.1
onnxruntime==1.16.1
onnxruntime-gpu==1.16.1

# Image and Video Processing
imageio==2.31.3
imageio-ffmpeg==0.4.9
scikit-image==0.21.0
albumentations==1.3.1

# Data Processing and Analysis
pandas==2.0.3
matplotlib==3.7.2
seaborn==0.12.2
plotly==5.17.0

# API and Web Development
requests==2.31.0
Werkzeug==2.3.7
gunicorn==21.2.0
python-dotenv==1.0.0
websockets==11.0.3

# Database
SQLAlchemy==2.0.21
psycopg2-binary==2.9.7

# File Handling and Utilities
PyYAML==6.0.1
python-multipart==0.0.6
aiofiles==23.2.1

# Performance and Optimization
numba==0.58.0
psutil==5.9.5
memory-profiler==0.61.0

# Model Optimization
tensorrt==8.6.1.post1  # For NVIDIA GPU optimization
openvino==2023.1.0     # For Intel optimization

# Monitoring and Logging
prometheus-client==0.17.1
structlog==23.1.0

# Security
cryptography==41.0.4
bcrypt==4.0.1

# Testing
pytest==7.4.2
pytest-cov==4.1.0
pytest-flask==1.2.0
pytest-asyncio==0.21.1

# Development Tools
black==23.7.0
flake8==6.0.0
isort==5.12.0

# Documentation
sphinx==7.1.2
sphinx-rtd-theme==1.3.0

# Deployment
docker==6.1.3
kubernetes==27.2.0

# Configuration Management
pydantic==2.3.0
hydra-core==1.3.2

# Async Support
asyncio==3.4.3
aiohttp==3.8.5

# Real-time Communication
socketio==5.8.0
python-socketio==5.8.0

# Video Streaming
streamlit==1.26.0  # For quick prototyping and demos
gradio==3.44.4     # Alternative web interface

# YOLO-specific dependencies
ultralytics[export]==8.0.196  # Includes export dependencies

# Edge Computing Support
tflite-runtime==2.13.0  # TensorFlow Lite for edge devices
onnxruntime-tools==1.16.1  # ONNX optimization tools

# Camera and Hardware Support
v4l2py==0.6.0      # Video4Linux support (Linux cameras)
pygrabber==0.2     # Windows camera support

# Performance Profiling
line-profiler==4.1.1
py-spy==0.3.14

# Data Visualization for Analytics
bokeh==3.2.2
dash==2.14.1

# Model Serving
bentoml==1.1.6
mlflow==2.7.1

# Distributed Computing (for multi-camera setups)
celery==5.3.2
redis==4.6.0

# Time Series Analysis (for analytics)
statsmodels==0.14.0

# GPU Monitoring
gpustat==1.1.1
nvidia-ml-py==12.535.108

# Network and Streaming
rtsp-client==0.1.0
ffmpeg-python==0.2.0

# Configuration and Environment
python-decouple==3.8
environs==9.5.0

# Caching
diskcache==5.6.3
joblib==1.3.2

# Serialization
pickle5==0.0.12
cloudpickle==2.2.1

# System Integration
pyserial==3.5      # For hardware integration
RPi.GPIO==0.7.1    # Raspberry Pi GPIO (if deploying on Pi)

# Quality Assurance
mypy==1.5.1
bandit==1.7.5

# Jupyter Support (for development and analysis)
jupyter==1.0.0
ipywidgets==8.1.1
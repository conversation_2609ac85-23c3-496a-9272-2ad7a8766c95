MIT License

Copyright (c) 2024 Hasif50 - Real-Time Object Detection for Smart Cameras

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

<PERSON><PERSON><PERSON><PERSON><PERSON> TERMS AND CONDITIONS:

COMPUTER VISION AND AI DISCLAIMER:

This software is designed for educational, research, and development purposes. 
The object detection capabilities provided by this software should not be used 
as the sole basis for critical decision-making in security, surveillance, or 
safety-critical applications without proper validation and human oversight.

The developers and contributors of this software are not responsible for any 
decisions made based on the output of the object detection algorithms. Users 
are responsible for ensuring the appropriate use of this technology in 
compliance with applicable laws and regulations.

PRIVACY AND SURVEILLANCE NOTICE:

This software processes video streams and may capture, analyze, or store images 
containing people and private property. Users must ensure compliance with all 
applicable privacy laws, surveillance regulations, and data protection 
requirements in their jurisdiction.

Users are responsible for:
- Obtaining proper consent for video recording and analysis
- Implementing appropriate data protection measures
- Complying with local privacy and surveillance laws
- Ensuring ethical use of computer vision technology

PERFORMANCE DISCLAIMER:

The accuracy and performance of object detection models may vary based on:
- Environmental conditions (lighting, weather, camera quality)
- Object types, sizes, and orientations
- Model training data and limitations
- Hardware capabilities and configuration

Real-time performance is dependent on hardware specifications and system 
configuration. The software may not achieve real-time performance on all 
hardware configurations.

THIRD-PARTY MODELS AND DEPENDENCIES:

This software may use pre-trained models and libraries from third parties, 
including but not limited to:
- YOLO models from Ultralytics
- OpenCV computer vision library
- PyTorch and TensorFlow frameworks

Users should review and comply with the licenses and terms of use for all 
third-party components and models used with this software.

---

Developed from Hasif's Workspace
GitHub: https://github.com/Hasif50
Project: Real-Time Object Detection for Smart Cameras
"""
Real-Time Object Detection for Smart Cameras - Image Processing Utilities
Developed from Hasif's Workspace

This module contains image processing utilities for the real-time object detection system.
"""

import cv2
import numpy as np
import logging
from typing import Tuple, Optional, List, Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class ImageProcessor:
    """
    Image processing utilities for object detection preprocessing and postprocessing.
    """

    @staticmethod
    def resize_image(
        image: np.ndarray,
        target_size: Tuple[int, int],
        maintain_aspect_ratio: bool = True,
    ) -> np.ndarray:
        """
        Resize image to target size.

        Args:
            image: Input image
            target_size: Target (width, height)
            maintain_aspect_ratio: Whether to maintain aspect ratio

        Returns:
            Resized image
        """
        try:
            if maintain_aspect_ratio:
                return ImageProcessor._resize_with_aspect_ratio(image, target_size)
            else:
                return cv2.resize(image, target_size)
        except Exception as e:
            logger.error(f"Error resizing image: {str(e)}")
            return image

    @staticmethod
    def _resize_with_aspect_ratio(
        image: np.ndarray, target_size: Tuple[int, int]
    ) -> np.ndarray:
        """Resize image while maintaining aspect ratio."""
        h, w = image.shape[:2]
        target_w, target_h = target_size

        # Calculate scaling factor
        scale = min(target_w / w, target_h / h)

        # Calculate new dimensions
        new_w = int(w * scale)
        new_h = int(h * scale)

        # Resize image
        resized = cv2.resize(image, (new_w, new_h))

        # Create padded image
        padded = np.zeros((target_h, target_w, 3), dtype=np.uint8)

        # Calculate padding offsets
        y_offset = (target_h - new_h) // 2
        x_offset = (target_w - new_w) // 2

        # Place resized image in center
        padded[y_offset : y_offset + new_h, x_offset : x_offset + new_w] = resized

        return padded

    @staticmethod
    def normalize_image(
        image: np.ndarray, mean: List[float] = None, std: List[float] = None
    ) -> np.ndarray:
        """
        Normalize image pixel values.

        Args:
            image: Input image
            mean: Mean values for normalization
            std: Standard deviation values for normalization

        Returns:
            Normalized image
        """
        try:
            # Convert to float32
            normalized = image.astype(np.float32) / 255.0

            if mean is not None and std is not None:
                mean = np.array(mean, dtype=np.float32)
                std = np.array(std, dtype=np.float32)
                normalized = (normalized - mean) / std

            return normalized
        except Exception as e:
            logger.error(f"Error normalizing image: {str(e)}")
            return image

    @staticmethod
    def preprocess_for_detection(
        image: np.ndarray, input_size: Tuple[int, int]
    ) -> np.ndarray:
        """
        Preprocess image for object detection.

        Args:
            image: Input image
            input_size: Model input size (width, height)

        Returns:
            Preprocessed image
        """
        try:
            # Resize image
            resized = ImageProcessor.resize_image(image, input_size)

            # Convert BGR to RGB
            rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)

            # Normalize
            normalized = ImageProcessor.normalize_image(rgb_image)

            # Transpose to CHW format
            transposed = np.transpose(normalized, (2, 0, 1))

            # Add batch dimension
            batched = np.expand_dims(transposed, axis=0)

            return batched
        except Exception as e:
            logger.error(f"Error preprocessing image: {str(e)}")
            return image

    @staticmethod
    def enhance_image(
        image: np.ndarray,
        brightness: float = 0,
        contrast: float = 1.0,
        saturation: float = 1.0,
    ) -> np.ndarray:
        """
        Enhance image with brightness, contrast, and saturation adjustments.

        Args:
            image: Input image
            brightness: Brightness adjustment (-100 to 100)
            contrast: Contrast multiplier (0.5 to 3.0)
            saturation: Saturation multiplier (0.0 to 2.0)

        Returns:
            Enhanced image
        """
        try:
            enhanced = image.copy()

            # Adjust brightness
            if brightness != 0:
                enhanced = cv2.add(
                    enhanced, np.ones(enhanced.shape, dtype=np.uint8) * brightness
                )

            # Adjust contrast
            if contrast != 1.0:
                enhanced = cv2.multiply(enhanced, np.ones(enhanced.shape) * contrast)

            # Adjust saturation
            if saturation != 1.0:
                hsv = cv2.cvtColor(enhanced, cv2.COLOR_BGR2HSV)
                hsv[:, :, 1] = cv2.multiply(hsv[:, :, 1], saturation)
                enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)

            return np.clip(enhanced, 0, 255).astype(np.uint8)
        except Exception as e:
            logger.error(f"Error enhancing image: {str(e)}")
            return image

    @staticmethod
    def apply_gaussian_blur(image: np.ndarray, kernel_size: int = 5) -> np.ndarray:
        """Apply Gaussian blur to image."""
        try:
            return cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)
        except Exception as e:
            logger.error(f"Error applying Gaussian blur: {str(e)}")
            return image

    @staticmethod
    def detect_edges(
        image: np.ndarray, low_threshold: int = 50, high_threshold: int = 150
    ) -> np.ndarray:
        """Detect edges using Canny edge detection."""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, low_threshold, high_threshold)
            return edges
        except Exception as e:
            logger.error(f"Error detecting edges: {str(e)}")
            return image

    @staticmethod
    def save_image(image: np.ndarray, filepath: str, quality: int = 95) -> bool:
        """
        Save image to file.

        Args:
            image: Image to save
            filepath: Output file path
            quality: JPEG quality (1-100)

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)

            # Set compression parameters
            if filepath.lower().endswith(".jpg") or filepath.lower().endswith(".jpeg"):
                params = [cv2.IMWRITE_JPEG_QUALITY, quality]
            elif filepath.lower().endswith(".png"):
                params = [cv2.IMWRITE_PNG_COMPRESSION, 9]
            else:
                params = []

            success = cv2.imwrite(filepath, image, params)

            if success:
                logger.info(f"Image saved to: {filepath}")
            else:
                logger.error(f"Failed to save image to: {filepath}")

            return success
        except Exception as e:
            logger.error(f"Error saving image: {str(e)}")
            return False

    @staticmethod
    def load_image(filepath: str) -> Optional[np.ndarray]:
        """
        Load image from file.

        Args:
            filepath: Path to image file

        Returns:
            Loaded image or None if failed
        """
        try:
            if not Path(filepath).exists():
                logger.error(f"Image file not found: {filepath}")
                return None

            image = cv2.imread(filepath)

            if image is None:
                logger.error(f"Failed to load image: {filepath}")
                return None

            logger.info(f"Image loaded from: {filepath}")
            return image
        except Exception as e:
            logger.error(f"Error loading image: {str(e)}")
            return None

    @staticmethod
    def get_image_info(image: np.ndarray) -> Dict[str, Any]:
        """
        Get image information.

        Args:
            image: Input image

        Returns:
            Dictionary containing image information
        """
        try:
            height, width = image.shape[:2]
            channels = image.shape[2] if len(image.shape) > 2 else 1

            return {
                "width": width,
                "height": height,
                "channels": channels,
                "dtype": str(image.dtype),
                "size_bytes": image.nbytes,
                "aspect_ratio": width / height,
            }
        except Exception as e:
            logger.error(f"Error getting image info: {str(e)}")
            return {}

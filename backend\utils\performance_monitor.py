"""
Real-Time Object Detection for Smart Cameras - Performance Monitor
Developed from Has<PERSON>'s Workspace

This module provides performance monitoring capabilities for tracking
FPS, CPU usage, memory consumption, and other system metrics.
"""

import time
import psutil
import logging
import threading
from collections import deque
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

try:
    import GPUtil

    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False
    logging.warning("GPUtil not available, GPU monitoring disabled")

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """
    Monitors system performance metrics including FPS, CPU, memory, and GPU usage.
    """

    def __init__(self, max_samples: int = 1000):
        """
        Initialize the performance monitor.

        Args:
            max_samples: Maximum number of samples to keep in memory
        """
        self.max_samples = max_samples

        # Performance metrics storage
        self.fps_samples = deque(maxlen=max_samples)
        self.cpu_samples = deque(maxlen=max_samples)
        self.memory_samples = deque(maxlen=max_samples)
        self.gpu_samples = deque(maxlen=max_samples)
        self.processing_time_samples = deque(maxlen=max_samples)

        # Timestamps for each sample
        self.timestamps = deque(maxlen=max_samples)

        # Current values
        self.current_fps = 0.0
        self.current_cpu = 0.0
        self.current_memory = 0.0
        self.current_gpu = 0.0
        self.current_processing_time = 0.0

        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None
        self.monitor_interval = 1.0  # seconds

        # Process information
        self.process = psutil.Process()

        logger.info("PerformanceMonitor initialized")

    def start_monitoring(self, interval: float = 1.0):
        """
        Start continuous performance monitoring.

        Args:
            interval: Monitoring interval in seconds
        """
        if self.is_monitoring:
            logger.warning("Performance monitoring is already running")
            return

        self.monitor_interval = interval
        self.is_monitoring = True

        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop, daemon=True
        )
        self.monitor_thread.start()

        logger.info(f"Performance monitoring started with {interval}s interval")

    def stop_monitoring(self):
        """Stop continuous performance monitoring."""
        if not self.is_monitoring:
            return

        self.is_monitoring = False

        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)

        logger.info("Performance monitoring stopped")

    def _monitoring_loop(self):
        """Internal monitoring loop running in separate thread."""
        try:
            while self.is_monitoring:
                self._collect_system_metrics()
                time.sleep(self.monitor_interval)
        except Exception as e:
            logger.error(f"Error in monitoring loop: {str(e)}")
        finally:
            self.is_monitoring = False

    def _collect_system_metrics(self):
        """Collect system performance metrics."""
        try:
            timestamp = datetime.now()

            # CPU usage
            cpu_percent = self.process.cpu_percent()
            self.current_cpu = cpu_percent
            self.cpu_samples.append(cpu_percent)

            # Memory usage
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024  # Convert to MB
            self.current_memory = memory_mb
            self.memory_samples.append(memory_mb)

            # GPU usage (if available)
            if GPU_AVAILABLE:
                try:
                    gpus = GPUtil.getGPUs()
                    if gpus:
                        gpu_usage = gpus[0].load * 100  # Convert to percentage
                        self.current_gpu = gpu_usage
                        self.gpu_samples.append(gpu_usage)
                    else:
                        self.gpu_samples.append(0.0)
                except Exception:
                    self.gpu_samples.append(0.0)
            else:
                self.gpu_samples.append(0.0)

            # Store timestamp
            self.timestamps.append(timestamp)

        except Exception as e:
            logger.error(f"Error collecting system metrics: {str(e)}")

    def update_fps(self, fps: float):
        """
        Update FPS measurement.

        Args:
            fps: Current FPS value
        """
        self.current_fps = fps
        self.fps_samples.append(fps)

        # Ensure timestamps are in sync
        if len(self.timestamps) < len(self.fps_samples):
            self.timestamps.append(datetime.now())

    def update_processing_time(self, processing_time: float):
        """
        Update processing time measurement.

        Args:
            processing_time: Processing time in seconds
        """
        self.current_processing_time = processing_time
        self.processing_time_samples.append(processing_time)

    def get_current_fps(self) -> float:
        """Get current FPS."""
        return self.current_fps

    def get_average_fps(self, window_seconds: Optional[float] = None) -> float:
        """
        Get average FPS over a time window.

        Args:
            window_seconds: Time window in seconds (None for all samples)

        Returns:
            Average FPS
        """
        if not self.fps_samples:
            return 0.0

        if window_seconds is None:
            return sum(self.fps_samples) / len(self.fps_samples)

        # Filter samples within time window
        cutoff_time = datetime.now() - timedelta(seconds=window_seconds)
        recent_fps = []

        for i, timestamp in enumerate(reversed(self.timestamps)):
            if timestamp >= cutoff_time and i < len(self.fps_samples):
                recent_fps.append(list(self.fps_samples)[-(i + 1)])
            else:
                break

        return sum(recent_fps) / len(recent_fps) if recent_fps else 0.0

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive performance statistics.

        Returns:
            Dictionary containing performance statistics
        """
        stats = {
            "timestamp": datetime.now().isoformat(),
            "sample_count": len(self.fps_samples),
            "monitoring_active": self.is_monitoring,
        }

        # FPS statistics
        if self.fps_samples:
            fps_list = list(self.fps_samples)
            stats["fps"] = {
                "current": self.current_fps,
                "average": sum(fps_list) / len(fps_list),
                "min": min(fps_list),
                "max": max(fps_list),
                "last_10_avg": sum(fps_list[-10:]) / min(10, len(fps_list)),
            }
        else:
            stats["fps"] = {
                "current": 0.0,
                "average": 0.0,
                "min": 0.0,
                "max": 0.0,
                "last_10_avg": 0.0,
            }

        # CPU statistics
        if self.cpu_samples:
            cpu_list = list(self.cpu_samples)
            stats["cpu"] = {
                "current": self.current_cpu,
                "average": sum(cpu_list) / len(cpu_list),
                "min": min(cpu_list),
                "max": max(cpu_list),
            }
        else:
            stats["cpu"] = {"current": 0.0, "average": 0.0, "min": 0.0, "max": 0.0}

        # Memory statistics
        if self.memory_samples:
            memory_list = list(self.memory_samples)
            stats["memory"] = {
                "current_mb": self.current_memory,
                "average_mb": sum(memory_list) / len(memory_list),
                "min_mb": min(memory_list),
                "max_mb": max(memory_list),
            }
        else:
            stats["memory"] = {
                "current_mb": 0.0,
                "average_mb": 0.0,
                "min_mb": 0.0,
                "max_mb": 0.0,
            }

        # GPU statistics
        if self.gpu_samples:
            gpu_list = list(self.gpu_samples)
            stats["gpu"] = {
                "current": self.current_gpu,
                "average": sum(gpu_list) / len(gpu_list),
                "min": min(gpu_list),
                "max": max(gpu_list),
                "available": GPU_AVAILABLE,
            }
        else:
            stats["gpu"] = {
                "current": 0.0,
                "average": 0.0,
                "min": 0.0,
                "max": 0.0,
                "available": GPU_AVAILABLE,
            }

        # Processing time statistics
        if self.processing_time_samples:
            proc_time_list = list(self.processing_time_samples)
            stats["processing_time"] = {
                "current_ms": self.current_processing_time * 1000,
                "average_ms": (sum(proc_time_list) / len(proc_time_list)) * 1000,
                "min_ms": min(proc_time_list) * 1000,
                "max_ms": max(proc_time_list) * 1000,
            }
        else:
            stats["processing_time"] = {
                "current_ms": 0.0,
                "average_ms": 0.0,
                "min_ms": 0.0,
                "max_ms": 0.0,
            }

        return stats

    def get_recent_samples(self, count: int = 100) -> Dict[str, List]:
        """
        Get recent performance samples.

        Args:
            count: Number of recent samples to return

        Returns:
            Dictionary containing recent samples
        """
        count = min(count, len(self.fps_samples))

        return {
            "timestamps": [ts.isoformat() for ts in list(self.timestamps)[-count:]],
            "fps": list(self.fps_samples)[-count:],
            "cpu": list(self.cpu_samples)[-count:],
            "memory": list(self.memory_samples)[-count:],
            "gpu": list(self.gpu_samples)[-count:],
            "processing_time": list(self.processing_time_samples)[-count:],
        }

    def check_performance_alerts(
        self, thresholds: Dict[str, float]
    ) -> List[Dict[str, Any]]:
        """
        Check for performance alerts based on thresholds.

        Args:
            thresholds: Dictionary of performance thresholds

        Returns:
            List of alert dictionaries
        """
        alerts = []

        # Check FPS threshold
        min_fps = thresholds.get("min_fps", 15.0)
        if self.current_fps < min_fps:
            alerts.append(
                {
                    "type": "low_fps",
                    "message": f"FPS below threshold: {self.current_fps:.1f} < {min_fps}",
                    "severity": "warning",
                    "value": self.current_fps,
                    "threshold": min_fps,
                }
            )

        # Check CPU threshold
        max_cpu = thresholds.get("max_cpu", 80.0)
        if self.current_cpu > max_cpu:
            alerts.append(
                {
                    "type": "high_cpu",
                    "message": f"CPU usage above threshold: {self.current_cpu:.1f}% > {max_cpu}%",
                    "severity": "warning",
                    "value": self.current_cpu,
                    "threshold": max_cpu,
                }
            )

        # Check memory threshold
        max_memory = thresholds.get("max_memory_mb", 2048.0)
        if self.current_memory > max_memory:
            alerts.append(
                {
                    "type": "high_memory",
                    "message": f"Memory usage above threshold: {self.current_memory:.1f}MB > {max_memory}MB",
                    "severity": "warning",
                    "value": self.current_memory,
                    "threshold": max_memory,
                }
            )

        # Check GPU threshold (if available)
        if GPU_AVAILABLE:
            max_gpu = thresholds.get("max_gpu", 90.0)
            if self.current_gpu > max_gpu:
                alerts.append(
                    {
                        "type": "high_gpu",
                        "message": f"GPU usage above threshold: {self.current_gpu:.1f}% > {max_gpu}%",
                        "severity": "warning",
                        "value": self.current_gpu,
                        "threshold": max_gpu,
                    }
                )

        return alerts

    def reset_metrics(self):
        """Reset all performance metrics."""
        self.fps_samples.clear()
        self.cpu_samples.clear()
        self.memory_samples.clear()
        self.gpu_samples.clear()
        self.processing_time_samples.clear()
        self.timestamps.clear()

        self.current_fps = 0.0
        self.current_cpu = 0.0
        self.current_memory = 0.0
        self.current_gpu = 0.0
        self.current_processing_time = 0.0

        logger.info("Performance metrics reset")

    def export_metrics(self, filepath: str, format: str = "csv") -> bool:
        """
        Export performance metrics to file.

        Args:
            filepath: Path to save the metrics file
            format: Export format ('csv' or 'json')

        Returns:
            True if export successful, False otherwise
        """
        try:
            if format.lower() == "csv":
                return self._export_csv(filepath)
            elif format.lower() == "json":
                return self._export_json(filepath)
            else:
                logger.error(f"Unsupported export format: {format}")
                return False
        except Exception as e:
            logger.error(f"Error exporting metrics: {str(e)}")
            return False

    def _export_csv(self, filepath: str) -> bool:
        """Export metrics to CSV format."""
        import csv

        with open(filepath, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.writer(csvfile)

            # Write header
            writer.writerow(
                [
                    "timestamp",
                    "fps",
                    "cpu_percent",
                    "memory_mb",
                    "gpu_percent",
                    "processing_time_ms",
                ]
            )

            # Write data
            for i in range(len(self.timestamps)):
                row = [
                    self.timestamps[i].isoformat(),
                    self.fps_samples[i] if i < len(self.fps_samples) else "",
                    self.cpu_samples[i] if i < len(self.cpu_samples) else "",
                    self.memory_samples[i] if i < len(self.memory_samples) else "",
                    self.gpu_samples[i] if i < len(self.gpu_samples) else "",
                    self.processing_time_samples[i] * 1000
                    if i < len(self.processing_time_samples)
                    else "",
                ]
                writer.writerow(row)

        logger.info(f"Metrics exported to CSV: {filepath}")
        return True

    def _export_json(self, filepath: str) -> bool:
        """Export metrics to JSON format."""
        import json

        data = {
            "export_timestamp": datetime.now().isoformat(),
            "statistics": self.get_statistics(),
            "samples": self.get_recent_samples(len(self.timestamps)),
        }

        with open(filepath, "w", encoding="utf-8") as jsonfile:
            json.dump(data, jsonfile, indent=2)

        logger.info(f"Metrics exported to JSON: {filepath}")
        return True

    def __del__(self):
        """Destructor to ensure monitoring is stopped."""
        self.stop_monitoring()

#!/usr/bin/env python3
"""
Real-Time Object Detection for Smart Cameras - Main Detection Script
Developed from Hasif's Workspace

This module serves as the main entry point for the real-time object detection system.
It handles camera input, model inference, and real-time video processing.
"""

import cv2
import time
import argparse
import logging
import threading
from pathlib import Path
from typing import Dict, List, Tuple, Optional

import numpy as np
from ultralytics import YOLO
import torch

from backend.detection.detector import ObjectDetector
from backend.detection.camera_manager import CameraManager
from backend.detection.stream_processor import StreamProcessor
from backend.utils.config_loader import ConfigLoader
from backend.utils.performance_monitor import PerformanceMonitor
from backend.app import create_app

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class RealTimeDetector:
    """
    Main class for real-time object detection system.
    Handles camera input, model inference, and video output.
    """

    def __init__(self, config_path: str = "backend/config/detection_config.yaml"):
        """Initialize the real-time detector."""
        self.config = ConfigLoader.load_config(config_path)
        self.detector = ObjectDetector(self.config)
        self.camera_manager = CameraManager(self.config)
        self.stream_processor = StreamProcessor(self.config)
        self.performance_monitor = PerformanceMonitor()

        # Detection state
        self.is_running = False
        self.detection_thread = None
        self.frame_count = 0
        self.total_detections = 0

        # Flask app for web interface
        self.app = create_app()

        logger.info("Real-Time Object Detection System initialized")
        logger.info(
            f"Using model: {self.config.get('model', {}).get('name', 'default')}"
        )
        logger.info(f"Camera index: {self.config.get('camera', {}).get('index', 0)}")

    def start_detection(
        self,
        camera_index: int = 0,
        model_name: str = "yolov8n",
        save_output: bool = False,
        display: bool = True,
    ) -> bool:
        """
        Start real-time object detection.

        Args:
            camera_index: Camera device index
            model_name: Name of the detection model to use
            save_output: Whether to save detection results
            display: Whether to display video output

        Returns:
            True if detection started successfully, False otherwise
        """
        try:
            if self.is_running:
                logger.warning("Detection is already running")
                return False

            # Initialize camera
            if not self.camera_manager.initialize_camera(camera_index):
                logger.error("Failed to initialize camera")
                return False

            # Load detection model
            if not self.detector.load_model(model_name):
                logger.error(f"Failed to load model: {model_name}")
                return False

            # Start detection thread
            self.is_running = True
            self.detection_thread = threading.Thread(
                target=self._detection_loop, args=(save_output, display), daemon=True
            )
            self.detection_thread.start()

            logger.info("Real-time detection started successfully")
            return True

        except Exception as e:
            logger.error(f"Error starting detection: {str(e)}")
            return False

    def stop_detection(self) -> Dict:
        """
        Stop real-time object detection.

        Returns:
            Dictionary with detection statistics
        """
        try:
            if not self.is_running:
                logger.warning("Detection is not running")
                return {}

            self.is_running = False

            # Wait for detection thread to finish
            if self.detection_thread and self.detection_thread.is_alive():
                self.detection_thread.join(timeout=5.0)

            # Release camera resources
            self.camera_manager.release_camera()

            # Get final statistics
            stats = self.performance_monitor.get_statistics()
            stats.update(
                {
                    "total_frames": self.frame_count,
                    "total_detections": self.total_detections,
                    "status": "stopped",
                }
            )

            logger.info("Real-time detection stopped")
            logger.info(
                f"Processed {self.frame_count} frames with {self.total_detections} detections"
            )

            return stats

        except Exception as e:
            logger.error(f"Error stopping detection: {str(e)}")
            return {"status": "error", "message": str(e)}

    def _detection_loop(self, save_output: bool = False, display: bool = True):
        """
        Main detection loop running in separate thread.

        Args:
            save_output: Whether to save detection results
            display: Whether to display video output
        """
        try:
            # Initialize video writer if saving output
            video_writer = None
            if save_output:
                video_writer = self._initialize_video_writer()

            # Initialize display window
            if display:
                cv2.namedWindow("Real-Time Object Detection", cv2.WINDOW_RESIZABLE)
                cv2.resizeWindow("Real-Time Object Detection", 1280, 720)

            logger.info("Starting detection loop...")

            while self.is_running:
                start_time = time.time()

                # Capture frame from camera
                frame = self.camera_manager.get_frame()
                if frame is None:
                    logger.warning("Failed to capture frame")
                    continue

                # Perform object detection
                detections = self.detector.detect(frame)

                # Process detections and draw annotations
                annotated_frame = self.stream_processor.process_frame(frame, detections)

                # Update statistics
                self.frame_count += 1
                self.total_detections += len(detections)

                # Calculate FPS
                processing_time = time.time() - start_time
                fps = 1.0 / processing_time if processing_time > 0 else 0
                self.performance_monitor.update_fps(fps)

                # Add FPS and detection count to frame
                self._add_info_overlay(annotated_frame, fps, len(detections))

                # Save frame if requested
                if video_writer is not None:
                    video_writer.write(annotated_frame)

                # Display frame
                if display:
                    cv2.imshow("Real-Time Object Detection", annotated_frame)

                    # Check for quit key
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord("q") or key == 27:  # 'q' or ESC
                        logger.info("Quit key pressed")
                        break

                # Log progress periodically
                if self.frame_count % 100 == 0:
                    logger.info(
                        f"Processed {self.frame_count} frames, "
                        f"Average FPS: {self.performance_monitor.get_average_fps():.1f}"
                    )

        except Exception as e:
            logger.error(f"Error in detection loop: {str(e)}")

        finally:
            # Cleanup
            if video_writer is not None:
                video_writer.release()

            if display:
                cv2.destroyAllWindows()

            logger.info("Detection loop ended")

    def _initialize_video_writer(self) -> Optional[cv2.VideoWriter]:
        """Initialize video writer for saving output."""
        try:
            # Create output directory
            output_dir = Path("data/output")
            output_dir.mkdir(parents=True, exist_ok=True)

            # Generate output filename with timestamp
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_path = output_dir / f"detection_output_{timestamp}.mp4"

            # Get camera properties
            width = int(self.camera_manager.camera.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.camera_manager.camera.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = self.config.get("camera", {}).get("fps", 30)

            # Initialize video writer
            fourcc = cv2.VideoWriter_fourcc(*"mp4v")
            video_writer = cv2.VideoWriter(
                str(output_path), fourcc, fps, (width, height)
            )

            if video_writer.isOpened():
                logger.info(f"Video output will be saved to: {output_path}")
                return video_writer
            else:
                logger.error("Failed to initialize video writer")
                return None

        except Exception as e:
            logger.error(f"Error initializing video writer: {str(e)}")
            return None

    def _add_info_overlay(self, frame: np.ndarray, fps: float, detection_count: int):
        """Add information overlay to frame."""
        try:
            # Add semi-transparent background
            overlay = frame.copy()
            cv2.rectangle(overlay, (10, 10), (400, 100), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)

            # Add text information
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.7
            color = (255, 255, 255)
            thickness = 2

            # FPS
            cv2.putText(
                frame, f"FPS: {fps:.1f}", (20, 35), font, font_scale, color, thickness
            )

            # Detection count
            cv2.putText(
                frame,
                f"Detections: {detection_count}",
                (20, 65),
                font,
                font_scale,
                color,
                thickness,
            )

            # Frame count
            cv2.putText(
                frame,
                f"Frame: {self.frame_count}",
                (20, 95),
                font,
                font_scale,
                color,
                thickness,
            )

        except Exception as e:
            logger.error(f"Error adding info overlay: {str(e)}")

    def get_status(self) -> Dict:
        """Get current detection status and statistics."""
        return {
            "is_running": self.is_running,
            "frame_count": self.frame_count,
            "total_detections": self.total_detections,
            "current_fps": self.performance_monitor.get_current_fps(),
            "average_fps": self.performance_monitor.get_average_fps(),
            "model_name": self.detector.current_model_name,
            "camera_index": self.camera_manager.current_camera_index,
        }

    def run_web_interface(
        self, host: str = "127.0.0.1", port: int = 5000, debug: bool = True
    ):
        """Run the web interface for monitoring and control."""
        try:
            logger.info(f"Starting web interface on http://{host}:{port}")
            self.app.run(host=host, port=port, debug=debug, threaded=True)
        except Exception as e:
            logger.error(f"Error running web interface: {str(e)}")


def main():
    """Main function for command-line interface."""
    parser = argparse.ArgumentParser(
        description="Real-Time Object Detection for Smart Cameras"
    )
    parser.add_argument(
        "--camera", type=int, default=0, help="Camera index (default: 0)"
    )
    parser.add_argument(
        "--model", type=str, default="yolov8n", help="Model name (default: yolov8n)"
    )
    parser.add_argument(
        "--save", action="store_true", help="Save detection output video"
    )
    parser.add_argument(
        "--no-display", action="store_true", help="Run without video display"
    )
    parser.add_argument("--web", action="store_true", help="Start web interface")
    parser.add_argument(
        "--host", type=str, default="127.0.0.1", help="Web interface host"
    )
    parser.add_argument("--port", type=int, default=5000, help="Web interface port")
    parser.add_argument("--config", type=str, help="Path to configuration file")

    args = parser.parse_args()

    try:
        # Initialize detector
        config_path = args.config or "backend/config/detection_config.yaml"
        detector = RealTimeDetector(config_path)

        if args.web:
            # Start web interface
            detector.run_web_interface(host=args.host, port=args.port)
        else:
            # Start command-line detection
            print("🎥 Real-Time Object Detection for Smart Cameras")
            print("   Developed from Hasif's Workspace")
            print("=" * 50)
            print(f"Camera: {args.camera}")
            print(f"Model: {args.model}")
            print(f"Save output: {args.save}")
            print(f"Display: {not args.no_display}")
            print("=" * 50)
            print("Press 'q' or ESC to quit")
            print()

            # Start detection
            if detector.start_detection(
                camera_index=args.camera,
                model_name=args.model,
                save_output=args.save,
                display=not args.no_display,
            ):
                try:
                    # Keep main thread alive
                    while detector.is_running:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\nInterrupt received, stopping detection...")

                # Stop detection and show statistics
                stats = detector.stop_detection()
                print("\nDetection Statistics:")
                print(f"Total frames: {stats.get('total_frames', 0)}")
                print(f"Total detections: {stats.get('total_detections', 0)}")
                print(f"Average FPS: {stats.get('average_fps', 0):.1f}")
            else:
                print("Failed to start detection")
                return 1

        return 0

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())

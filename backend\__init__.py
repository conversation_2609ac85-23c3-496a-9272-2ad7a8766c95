"""
Real-Time Object Detection for Smart Cameras - Backend Package
Developed from Hasif's Workspace

This package contains the backend components for the real-time object detection system,
including detection algorithms, camera management, and API endpoints.
"""

__version__ = "1.0.0"
__author__ = "Hasif50"
__description__ = "Real-Time Object Detection for Smart Cameras Backend"

# Package imports
from .app import create_app
from .detection import ObjectDetector, CameraManager, StreamProcessor
from .utils import Config<PERSON>oader, PerformanceMonitor

__all__ = [
    "create_app",
    "ObjectDetector",
    "CameraManager",
    "StreamProcessor",
    "ConfigLoader",
    "PerformanceMonitor",
]

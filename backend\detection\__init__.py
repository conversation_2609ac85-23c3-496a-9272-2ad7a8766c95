"""
Real-Time Object Detection for Smart Cameras - Detection Package
Developed from Hasif's Workspace

This package contains the core detection components including object detection,
camera management, and stream processing functionality.
"""

from .detector import ObjectDetector
from .camera_manager import CameraManager
from .stream_processor import StreamProcessor

__all__ = ["ObjectDetector", "CameraManager", "StreamProcessor"]

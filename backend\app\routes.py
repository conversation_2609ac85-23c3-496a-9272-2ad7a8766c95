"""
Real-Time Object Detection for Smart Cameras - API Routes
Developed from Hasif's Workspace

This module contains Flask routes for the real-time object detection API,
including detection control, session management, and system monitoring.
"""

import os
import time
import logging
from datetime import datetime
from flask import Blueprint, request, jsonify, render_template, current_app
from backend.app import db
from backend.app.models import (
    DetectionSession,
    Detection,
    ModelInfo,
    SystemConfig,
    PerformanceMetrics,
    AuditLog,
)

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprints
main_bp = Blueprint("main", __name__)
api_bp = Blueprint("api", __name__)
detection_bp = Blueprint("detection", __name__)

# Global detection state (in production, use Redis or database)
detection_state = {
    "is_running": False,
    "current_session": None,
    "detector_instance": None,
}


@main_bp.route("/")
def index():
    """Main page route."""
    return render_template("index.html")


@main_bp.route("/health")
def health_check():
    """Health check endpoint."""
    return jsonify(
        {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0",
            "service": "Real-Time Object Detection for Smart Cameras",
        }
    )


# API Routes
@api_bp.route("/health")
def api_health():
    """API health check."""
    try:
        # Check database connection
        db.session.execute("SELECT 1")
        db_status = "connected"
    except Exception as e:
        db_status = f"error: {str(e)}"

    return jsonify(
        {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "database": db_status,
            "detection_running": detection_state["is_running"],
            "version": "1.0.0",
        }
    )


@api_bp.route("/models", methods=["GET"])
def get_models():
    """Get available detection models."""
    try:
        models = ModelInfo.query.filter_by(is_active=True).all()
        return jsonify(
            {"status": "success", "models": [model.to_dict() for model in models]}
        )
    except Exception as e:
        logger.error(f"Error getting models: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route("/models", methods=["POST"])
def add_model():
    """Add a new detection model."""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ["name", "display_name", "model_path", "model_type"]
        for field in required_fields:
            if field not in data:
                return jsonify(
                    {"status": "error", "message": f"Missing field: {field}"}
                ), 400

        # Check if model already exists
        existing_model = ModelInfo.query.filter_by(name=data["name"]).first()
        if existing_model:
            return jsonify({"status": "error", "message": "Model already exists"}), 400

        # Create new model
        model = ModelInfo(
            name=data["name"],
            display_name=data["display_name"],
            description=data.get("description", ""),
            model_path=data["model_path"],
            model_type=data["model_type"],
            input_size=data.get("input_size", "640x640"),
            num_classes=data.get("num_classes", 80),
            map_score=data.get("map_score"),
            inference_time=data.get("inference_time"),
            model_size=data.get("model_size"),
        )

        if "class_names" in data:
            model.set_class_names(data["class_names"])

        db.session.add(model)
        db.session.commit()

        # Log action
        log_audit_action("model_added", "model", model.id, data)

        return jsonify(
            {
                "status": "success",
                "message": "Model added successfully",
                "model": model.to_dict(),
            }
        )

    except Exception as e:
        logger.error(f"Error adding model: {str(e)}")
        db.session.rollback()
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route("/sessions", methods=["GET"])
def get_sessions():
    """Get detection sessions."""
    try:
        page = request.args.get("page", 1, type=int)
        per_page = request.args.get("per_page", 20, type=int)
        status = request.args.get("status")

        query = DetectionSession.query
        if status:
            query = query.filter_by(status=status)

        sessions = query.order_by(DetectionSession.start_time.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return jsonify(
            {
                "status": "success",
                "sessions": [session.to_dict() for session in sessions.items],
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total": sessions.total,
                    "pages": sessions.pages,
                },
            }
        )

    except Exception as e:
        logger.error(f"Error getting sessions: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route("/sessions/<int:session_id>", methods=["GET"])
def get_session(session_id):
    """Get specific detection session."""
    try:
        session = DetectionSession.query.get_or_404(session_id)

        # Get recent detections for this session
        recent_detections = (
            Detection.query.filter_by(session_id=session_id)
            .order_by(Detection.timestamp.desc())
            .limit(100)
            .all()
        )

        session_data = session.to_dict()
        session_data["recent_detections"] = [det.to_dict() for det in recent_detections]

        return jsonify({"status": "success", "session": session_data})

    except Exception as e:
        logger.error(f"Error getting session {session_id}: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route("/config", methods=["GET"])
def get_config():
    """Get system configuration."""
    try:
        configs = SystemConfig.query.all()
        config_dict = {config.key: config.get_value() for config in configs}

        return jsonify({"status": "success", "config": config_dict})

    except Exception as e:
        logger.error(f"Error getting config: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route("/config", methods=["POST"])
def update_config():
    """Update system configuration."""
    try:
        data = request.get_json()

        for key, value in data.items():
            config = SystemConfig.query.filter_by(key=key).first()
            if config:
                config.set_value(value)
                config.updated_at = datetime.utcnow()
            else:
                # Create new config entry
                config = SystemConfig(
                    key=key,
                    value=str(value),
                    config_type="string",  # Default type
                )
                db.session.add(config)

        db.session.commit()

        # Log action
        log_audit_action("config_updated", "config", None, data)

        return jsonify(
            {"status": "success", "message": "Configuration updated successfully"}
        )

    except Exception as e:
        logger.error(f"Error updating config: {str(e)}")
        db.session.rollback()
        return jsonify({"status": "error", "message": str(e)}), 500


# Detection Routes
@detection_bp.route("/start", methods=["POST"])
def start_detection():
    """Start real-time object detection."""
    try:
        if detection_state["is_running"]:
            return jsonify(
                {"status": "error", "message": "Detection is already running"}
            ), 400

        data = request.get_json() or {}

        # Get parameters
        camera_index = data.get("camera_index", 0)
        model_name = data.get("model", "yolov8n")
        confidence_threshold = data.get("confidence_threshold", 0.5)
        nms_threshold = data.get("nms_threshold", 0.4)
        save_output = data.get("save_output", False)
        session_name = data.get("session_name", f"Session_{int(time.time())}")

        # Validate model exists
        model = ModelInfo.query.filter_by(name=model_name, is_active=True).first()
        if not model:
            return jsonify(
                {
                    "status": "error",
                    "message": f"Model {model_name} not found or inactive",
                }
            ), 400

        # Create detection session
        session = DetectionSession(
            session_name=session_name,
            model_name=model_name,
            camera_index=camera_index,
            confidence_threshold=confidence_threshold,
            nms_threshold=nms_threshold,
            save_output=save_output,
            status="active",
        )

        db.session.add(session)
        db.session.commit()

        # Initialize detector (this would be done by the actual detector instance)
        detection_state["is_running"] = True
        detection_state["current_session"] = session.id

        # Log action
        log_audit_action(
            "detection_started",
            "session",
            session.id,
            {"model_name": model_name, "camera_index": camera_index},
        )

        return jsonify(
            {
                "status": "success",
                "message": "Detection started successfully",
                "session_id": session.id,
                "session": session.to_dict(),
            }
        )

    except Exception as e:
        logger.error(f"Error starting detection: {str(e)}")
        db.session.rollback()
        return jsonify({"status": "error", "message": str(e)}), 500


@detection_bp.route("/stop", methods=["POST"])
def stop_detection():
    """Stop real-time object detection."""
    try:
        if not detection_state["is_running"]:
            return jsonify(
                {"status": "error", "message": "Detection is not running"}
            ), 400

        session_id = detection_state["current_session"]
        if session_id:
            session = DetectionSession.query.get(session_id)
            if session:
                session.end_time = datetime.utcnow()
                session.status = "stopped"
                db.session.commit()

        # Stop detector
        detection_state["is_running"] = False
        detection_state["current_session"] = None

        # Log action
        log_audit_action("detection_stopped", "session", session_id, {})

        return jsonify(
            {
                "status": "success",
                "message": "Detection stopped successfully",
                "session_id": session_id,
            }
        )

    except Exception as e:
        logger.error(f"Error stopping detection: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@detection_bp.route("/status", methods=["GET"])
def get_detection_status():
    """Get current detection status."""
    try:
        status_data = {
            "is_running": detection_state["is_running"],
            "current_session_id": detection_state["current_session"],
            "timestamp": datetime.utcnow().isoformat(),
        }

        if detection_state["current_session"]:
            session = DetectionSession.query.get(detection_state["current_session"])
            if session:
                status_data["session"] = session.to_dict()

                # Get recent performance metrics
                recent_metrics = (
                    PerformanceMetrics.query.filter_by(session_id=session.id)
                    .order_by(PerformanceMetrics.timestamp.desc())
                    .limit(10)
                    .all()
                )

                if recent_metrics:
                    latest_metric = recent_metrics[0]
                    status_data["performance"] = {
                        "fps": latest_metric.fps,
                        "cpu_usage": latest_metric.cpu_usage,
                        "memory_usage": latest_metric.memory_usage,
                        "gpu_usage": latest_metric.gpu_usage,
                        "processing_time": latest_metric.processing_time,
                    }

        return jsonify({"status": "success", "detection_status": status_data})

    except Exception as e:
        logger.error(f"Error getting detection status: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@detection_bp.route("/results", methods=["GET"])
def get_detection_results():
    """Get recent detection results."""
    try:
        session_id = request.args.get("session_id", detection_state["current_session"])
        limit = request.args.get("limit", 50, type=int)

        if not session_id:
            return jsonify({"status": "error", "message": "No active session"}), 400

        # Get recent detections
        detections = (
            Detection.query.filter_by(session_id=session_id)
            .order_by(Detection.timestamp.desc())
            .limit(limit)
            .all()
        )

        # Get session info
        session = DetectionSession.query.get(session_id)

        return jsonify(
            {
                "status": "success",
                "session_id": session_id,
                "session": session.to_dict() if session else None,
                "detections": [det.to_dict() for det in detections],
                "total_detections": len(detections),
            }
        )

    except Exception as e:
        logger.error(f"Error getting detection results: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route("/statistics", methods=["GET"])
def get_statistics():
    """Get system statistics."""
    try:
        # Get overall statistics
        total_sessions = DetectionSession.query.count()
        active_sessions = DetectionSession.query.filter_by(status="active").count()
        total_detections = Detection.query.count()

        # Get recent activity
        recent_sessions = (
            DetectionSession.query.order_by(DetectionSession.start_time.desc())
            .limit(5)
            .all()
        )

        # Calculate average performance
        avg_fps = db.session.query(db.func.avg(PerformanceMetrics.fps)).scalar() or 0

        return jsonify(
            {
                "status": "success",
                "statistics": {
                    "total_sessions": total_sessions,
                    "active_sessions": active_sessions,
                    "total_detections": total_detections,
                    "average_fps": round(avg_fps, 2),
                    "system_status": "healthy"
                    if not detection_state["is_running"] or active_sessions > 0
                    else "idle",
                },
                "recent_activity": [session.to_dict() for session in recent_sessions],
            }
        )

    except Exception as e:
        logger.error(f"Error getting statistics: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


def log_audit_action(action, resource, resource_id, details):
    """Log an audit action."""
    try:
        audit_log = AuditLog(
            action=action,
            resource=resource,
            resource_id=resource_id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent", ""),
        )
        audit_log.set_details(details)

        db.session.add(audit_log)
        db.session.commit()

    except Exception as e:
        logger.error(f"Error logging audit action: {str(e)}")


# Error handlers
@api_bp.errorhandler(404)
def not_found(error):
    return jsonify({"status": "error", "message": "Resource not found"}), 404


@api_bp.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return jsonify({"status": "error", "message": "Internal server error"}), 500

"""
Real-Time Object Detection for Smart Cameras - Database Models
Developed from Hasif's Workspace

This module contains SQLAlchemy models for storing detection sessions,
results, and system configuration data.
"""

from datetime import datetime
from backend.app import db
import json


class DetectionSession(db.Model):
    """Model for detection sessions."""

    __tablename__ = "detection_sessions"

    id = db.Column(db.Integer, primary_key=True)
    session_name = db.Column(db.String(100), nullable=False)
    model_name = db.Column(db.String(50), nullable=False)
    camera_index = db.Column(db.Integer, nullable=False, default=0)
    start_time = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    end_time = db.Column(db.DateTime)
    status = db.Column(
        db.String(20), nullable=False, default="active"
    )  # active, stopped, error

    # Session configuration
    confidence_threshold = db.Column(db.Float, default=0.5)
    nms_threshold = db.Column(db.Float, default=0.4)
    save_output = db.Column(db.Boolean, default=False)
    output_path = db.Column(db.String(255))

    # Session statistics
    total_frames = db.Column(db.Integer, default=0)
    total_detections = db.Column(db.Integer, default=0)
    average_fps = db.Column(db.Float, default=0.0)

    # Relationships
    detections = db.relationship(
        "Detection", backref="session", lazy=True, cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<DetectionSession {self.session_name}>"

    def to_dict(self):
        """Convert session to dictionary."""
        return {
            "id": self.id,
            "session_name": self.session_name,
            "model_name": self.model_name,
            "camera_index": self.camera_index,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "status": self.status,
            "confidence_threshold": self.confidence_threshold,
            "nms_threshold": self.nms_threshold,
            "save_output": self.save_output,
            "output_path": self.output_path,
            "total_frames": self.total_frames,
            "total_detections": self.total_detections,
            "average_fps": self.average_fps,
        }


class Detection(db.Model):
    """Model for individual object detections."""

    __tablename__ = "detections"

    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(
        db.Integer, db.ForeignKey("detection_sessions.id"), nullable=False
    )
    frame_number = db.Column(db.Integer, nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

    # Detection data
    class_name = db.Column(db.String(50), nullable=False)
    confidence = db.Column(db.Float, nullable=False)
    bbox_x = db.Column(db.Float, nullable=False)  # Bounding box coordinates
    bbox_y = db.Column(db.Float, nullable=False)
    bbox_width = db.Column(db.Float, nullable=False)
    bbox_height = db.Column(db.Float, nullable=False)

    # Additional metadata
    model_version = db.Column(db.String(50))
    processing_time = db.Column(db.Float)  # Time taken for this detection

    def __repr__(self):
        return f"<Detection {self.class_name} ({self.confidence:.2f})>"

    def to_dict(self):
        """Convert detection to dictionary."""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "frame_number": self.frame_number,
            "timestamp": self.timestamp.isoformat(),
            "class_name": self.class_name,
            "confidence": self.confidence,
            "bbox": {
                "x": self.bbox_x,
                "y": self.bbox_y,
                "width": self.bbox_width,
                "height": self.bbox_height,
            },
            "model_version": self.model_version,
            "processing_time": self.processing_time,
        }


class ModelInfo(db.Model):
    """Model for storing information about available detection models."""

    __tablename__ = "model_info"

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    display_name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    model_path = db.Column(db.String(255), nullable=False)
    model_type = db.Column(db.String(50), nullable=False)  # yolo, detectron2, etc.

    # Model specifications
    input_size = db.Column(db.String(50))  # e.g., "640x640"
    num_classes = db.Column(db.Integer)
    class_names = db.Column(db.Text)  # JSON string of class names

    # Performance metrics
    map_score = db.Column(db.Float)  # Mean Average Precision
    inference_time = db.Column(db.Float)  # Average inference time in ms
    model_size = db.Column(db.Float)  # Model size in MB

    # Status and metadata
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )

    def __repr__(self):
        return f"<ModelInfo {self.name}>"

    def get_class_names(self):
        """Get class names as a list."""
        if self.class_names:
            try:
                return json.loads(self.class_names)
            except json.JSONDecodeError:
                return []
        return []

    def set_class_names(self, class_names_list):
        """Set class names from a list."""
        self.class_names = json.dumps(class_names_list)

    def to_dict(self):
        """Convert model info to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "model_path": self.model_path,
            "model_type": self.model_type,
            "input_size": self.input_size,
            "num_classes": self.num_classes,
            "class_names": self.get_class_names(),
            "map_score": self.map_score,
            "inference_time": self.inference_time,
            "model_size": self.model_size,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class SystemConfig(db.Model):
    """Model for storing system configuration settings."""

    __tablename__ = "system_config"

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=False)
    description = db.Column(db.Text)
    config_type = db.Column(
        db.String(20), nullable=False
    )  # string, int, float, bool, json

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )

    def __repr__(self):
        return f"<SystemConfig {self.key}>"

    def get_value(self):
        """Get the value with appropriate type conversion."""
        if self.config_type == "int":
            return int(self.value)
        elif self.config_type == "float":
            return float(self.value)
        elif self.config_type == "bool":
            return self.value.lower() in ("true", "1", "yes", "on")
        elif self.config_type == "json":
            try:
                return json.loads(self.value)
            except json.JSONDecodeError:
                return {}
        else:
            return self.value

    def set_value(self, value):
        """Set the value with appropriate type conversion."""
        if self.config_type == "json":
            self.value = json.dumps(value)
        else:
            self.value = str(value)

    def to_dict(self):
        """Convert config to dictionary."""
        return {
            "id": self.id,
            "key": self.key,
            "value": self.get_value(),
            "description": self.description,
            "config_type": self.config_type,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class PerformanceMetrics(db.Model):
    """Model for storing system performance metrics."""

    __tablename__ = "performance_metrics"

    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(
        db.Integer, db.ForeignKey("detection_sessions.id"), nullable=True
    )
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

    # Performance data
    fps = db.Column(db.Float, nullable=False)
    cpu_usage = db.Column(db.Float)  # Percentage
    memory_usage = db.Column(db.Float)  # MB
    gpu_usage = db.Column(db.Float)  # Percentage
    gpu_memory = db.Column(db.Float)  # MB

    # Detection metrics
    detections_per_frame = db.Column(db.Float)
    average_confidence = db.Column(db.Float)
    processing_time = db.Column(db.Float)  # ms per frame

    def __repr__(self):
        return f"<PerformanceMetrics FPS:{self.fps:.1f}>"

    def to_dict(self):
        """Convert metrics to dictionary."""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "timestamp": self.timestamp.isoformat(),
            "fps": self.fps,
            "cpu_usage": self.cpu_usage,
            "memory_usage": self.memory_usage,
            "gpu_usage": self.gpu_usage,
            "gpu_memory": self.gpu_memory,
            "detections_per_frame": self.detections_per_frame,
            "average_confidence": self.average_confidence,
            "processing_time": self.processing_time,
        }


class AuditLog(db.Model):
    """Model for audit logging of system actions."""

    __tablename__ = "audit_logs"

    id = db.Column(db.Integer, primary_key=True)
    timestamp = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    action = db.Column(db.String(100), nullable=False)
    resource = db.Column(db.String(100))
    resource_id = db.Column(db.Integer)
    user_id = db.Column(db.String(100))  # For future user management
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(255))
    details = db.Column(db.Text)  # JSON string with additional details

    def __repr__(self):
        return f"<AuditLog {self.action}>"

    def get_details(self):
        """Get details as dictionary."""
        if self.details:
            try:
                return json.loads(self.details)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_details(self, details_dict):
        """Set details from dictionary."""
        self.details = json.dumps(details_dict)

    def to_dict(self):
        """Convert audit log to dictionary."""
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "action": self.action,
            "resource": self.resource,
            "resource_id": self.resource_id,
            "user_id": self.user_id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "details": self.get_details(),
        }

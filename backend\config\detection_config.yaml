# Real-Time Object Detection for Smart Cameras - Detection Configuration
# Developed from Hasif's Workspace

# Model configurations
models:
  yolov8n:
    path: "models/yolo/yolov8n.pt"
    display_name: "YOLOv8 Nano"
    description: "Fastest model, optimized for real-time applications"
    input_size: [640, 640]
    classes: 80
    confidence_threshold: 0.5
    nms_threshold: 0.4
    class_names:
      - "person"
      - "bicycle"
      - "car"
      - "motorcycle"
      - "airplane"
      - "bus"
      - "train"
      - "truck"
      - "boat"
      - "traffic light"
      - "fire hydrant"
      - "stop sign"
      - "parking meter"
      - "bench"
      - "bird"
      - "cat"
      - "dog"
      - "horse"
      - "sheep"
      - "cow"
      - "elephant"
      - "bear"
      - "zebra"
      - "giraffe"
      - "backpack"
      - "umbrella"
      - "handbag"
      - "tie"
      - "suitcase"
      - "frisbee"
      - "skis"
      - "snowboard"
      - "sports ball"
      - "kite"
      - "baseball bat"
      - "baseball glove"
      - "skateboard"
      - "surfboard"
      - "tennis racket"
      - "bottle"
      - "wine glass"
      - "cup"
      - "fork"
      - "knife"
      - "spoon"
      - "bowl"
      - "banana"
      - "apple"
      - "sandwich"
      - "orange"
      - "broccoli"
      - "carrot"
      - "hot dog"
      - "pizza"
      - "donut"
      - "cake"
      - "chair"
      - "couch"
      - "potted plant"
      - "bed"
      - "dining table"
      - "toilet"
      - "tv"
      - "laptop"
      - "mouse"
      - "remote"
      - "keyboard"
      - "cell phone"
      - "microwave"
      - "oven"
      - "toaster"
      - "sink"
      - "refrigerator"
      - "book"
      - "clock"
      - "vase"
      - "scissors"
      - "teddy bear"
      - "hair drier"
      - "toothbrush"

  yolov8s:
    path: "models/yolo/yolov8s.pt"
    display_name: "YOLOv8 Small"
    description: "Balanced speed and accuracy"
    input_size: [640, 640]
    classes: 80
    confidence_threshold: 0.5
    nms_threshold: 0.4
    class_names: &coco_classes
      - "person"
      - "bicycle"
      - "car"
      - "motorcycle"
      - "airplane"
      - "bus"
      - "train"
      - "truck"
      - "boat"
      - "traffic light"
      - "fire hydrant"
      - "stop sign"
      - "parking meter"
      - "bench"
      - "bird"
      - "cat"
      - "dog"
      - "horse"
      - "sheep"
      - "cow"
      - "elephant"
      - "bear"
      - "zebra"
      - "giraffe"
      - "backpack"
      - "umbrella"
      - "handbag"
      - "tie"
      - "suitcase"
      - "frisbee"
      - "skis"
      - "snowboard"
      - "sports ball"
      - "kite"
      - "baseball bat"
      - "baseball glove"
      - "skateboard"
      - "surfboard"
      - "tennis racket"
      - "bottle"
      - "wine glass"
      - "cup"
      - "fork"
      - "knife"
      - "spoon"
      - "bowl"
      - "banana"
      - "apple"
      - "sandwich"
      - "orange"
      - "broccoli"
      - "carrot"
      - "hot dog"
      - "pizza"
      - "donut"
      - "cake"
      - "chair"
      - "couch"
      - "potted plant"
      - "bed"
      - "dining table"
      - "toilet"
      - "tv"
      - "laptop"
      - "mouse"
      - "remote"
      - "keyboard"
      - "cell phone"
      - "microwave"
      - "oven"
      - "toaster"
      - "sink"
      - "refrigerator"
      - "book"
      - "clock"
      - "vase"
      - "scissors"
      - "teddy bear"
      - "hair drier"
      - "toothbrush"

  yolov8m:
    path: "models/yolo/yolov8m.pt"
    display_name: "YOLOv8 Medium"
    description: "Higher accuracy, moderate speed"
    input_size: [640, 640]
    classes: 80
    confidence_threshold: 0.5
    nms_threshold: 0.4
    class_names: *coco_classes

  yolov8l:
    path: "models/yolo/yolov8l.pt"
    display_name: "YOLOv8 Large"
    description: "High accuracy, slower inference"
    input_size: [640, 640]
    classes: 80
    confidence_threshold: 0.5
    nms_threshold: 0.4
    class_names: *coco_classes

  yolov8x:
    path: "models/yolo/yolov8x.pt"
    display_name: "YOLOv8 Extra Large"
    description: "Highest accuracy, slowest inference"
    input_size: [640, 640]
    classes: 80
    confidence_threshold: 0.5
    nms_threshold: 0.4
    class_names: *coco_classes

  # Custom models
  security_camera:
    path: "models/custom/security_camera.pt"
    display_name: "Security Camera Model"
    description: "Optimized for person and vehicle detection"
    input_size: [640, 640]
    classes: 4
    confidence_threshold: 0.6
    nms_threshold: 0.4
    class_names:
      - "person"
      - "car"
      - "truck"
      - "motorcycle"

  retail_analytics:
    path: "models/custom/retail_analytics.pt"
    display_name: "Retail Analytics Model"
    description: "Customer behavior and product interaction analysis"
    input_size: [640, 640]
    classes: 6
    confidence_threshold: 0.5
    nms_threshold: 0.4
    class_names:
      - "person"
      - "shopping_cart"
      - "product"
      - "shelf"
      - "checkout"
      - "queue"

# Camera configuration
camera:
  default_index: 0
  resolution: [640, 480]
  fps: 30
  buffer_size: 1
  auto_exposure: true
  auto_white_balance: true

  # Camera properties (adjust based on your camera)
  brightness: 0.5
  contrast: 0.5
  saturation: 0.5
  exposure: -1 # Auto exposure

# Detection processing configuration
detection:
  confidence_threshold: 0.5
  nms_threshold: 0.4
  max_detections: 100

  # Performance settings
  use_gpu: true
  batch_processing: false
  optimization: "onnx" # options: none, onnx, tensorrt

  # Tracking settings
  enable_tracking: false
  tracking_algorithm: "sort" # options: sort, deepsort, bytetrack
  max_track_age: 30
  min_track_hits: 3

# Display configuration
display:
  show_labels: true
  show_confidence: true
  show_fps: true
  show_timestamp: true
  show_detection_count: true

  # Visual settings
  font_scale: 0.6
  font_thickness: 2
  line_thickness: 2

  # Colors (BGR format)
  text_color: [255, 255, 255]
  background_color: [0, 0, 0]

  # Overlay settings
  overlay_alpha: 0.7
  info_panel_width: 400
  info_panel_height: 120

# Output configuration
output:
  save_detections: false
  save_video: false
  save_images: false

  # Output paths
  video_output_path: "data/output/videos"
  image_output_path: "data/output/images"
  detection_output_path: "data/output/detections"

  # Video settings
  video_codec: "mp4v"
  video_quality: 90

  # Image settings
  image_format: "jpg"
  image_quality: 95

# Performance monitoring
performance:
  enable_monitoring: true
  log_interval: 10 # seconds
  metrics_retention: 1000 # number of measurements to keep

  # Thresholds for alerts
  min_fps_threshold: 15
  max_cpu_threshold: 80
  max_memory_threshold: 80

# Regions of Interest (ROI)
roi:
  enable_roi: false
  regions: []
  # Example ROI configuration:
  # regions:
  #   - name: "entrance"
  #     polygon: [[100, 100], [300, 100], [300, 200], [100, 200]]
  #     classes: ["person"]
  #   - name: "parking"
  #     polygon: [[400, 200], [600, 200], [600, 400], [400, 400]]
  #     classes: ["car", "truck", "motorcycle"]

# Alert configuration
alerts:
  enable_alerts: false
  alert_types:
    - "person_detected"
    - "vehicle_detected"
    - "multiple_objects"

  # Alert thresholds
  person_count_threshold: 5
  vehicle_count_threshold: 3
  confidence_threshold: 0.8

  # Alert actions
  save_alert_image: true
  send_notification: false
  log_alert: true

# Advanced settings
advanced:
  # Model optimization
  enable_tensorrt: false
  tensorrt_precision: "fp16" # options: fp32, fp16, int8

  # Memory management
  max_memory_usage: 4096 # MB
  clear_cache_interval: 300 # seconds

  # Multi-threading
  num_worker_threads: 2
  enable_async_processing: false

  # Debugging
  debug_mode: false
  save_debug_images: false
  profile_performance: false

"""
Real-Time Object Detection for Smart Cameras - Security Utilities
Developed from Has<PERSON>'s Workspace

This module contains security utilities for authentication, authorization, and data protection.
"""

import hashlib
import secrets
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import re

logger = logging.getLogger(__name__)


class SecurityManager:
    """
    Security utilities for the real-time object detection system.
    """

    @staticmethod
    def generate_session_token() -> str:
        """
        Generate a secure session token.

        Returns:
            Secure random token string
        """
        return secrets.token_urlsafe(32)

    @staticmethod
    def hash_password(password: str, salt: Optional[str] = None) -> Dict[str, str]:
        """
        Hash a password with salt.

        Args:
            password: Password to hash
            salt: Optional salt (generated if not provided)

        Returns:
            Dictionary with hashed password and salt
        """
        if salt is None:
            salt = secrets.token_hex(16)

        # Combine password and salt
        salted_password = password + salt

        # Hash using SHA-256
        hashed = hashlib.sha256(salted_password.encode()).hexdigest()

        return {"hash": hashed, "salt": salt}

    @staticmethod
    def verify_password(password: str, stored_hash: str, salt: str) -> bool:
        """
        Verify a password against stored hash.

        Args:
            password: Password to verify
            stored_hash: Stored password hash
            salt: Salt used for hashing

        Returns:
            True if password is correct, False otherwise
        """
        try:
            # Hash the provided password with the stored salt
            result = SecurityManager.hash_password(password, salt)
            return result["hash"] == stored_hash
        except Exception as e:
            logger.error(f"Error verifying password: {str(e)}")
            return False

    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """
        Validate password strength.

        Args:
            password: Password to validate

        Returns:
            Dictionary with validation results
        """
        result = {"is_valid": False, "score": 0, "issues": []}

        try:
            # Check minimum length
            if len(password) < 8:
                result["issues"].append("Password must be at least 8 characters long")
            else:
                result["score"] += 1

            # Check for uppercase letters
            if not re.search(r"[A-Z]", password):
                result["issues"].append(
                    "Password must contain at least one uppercase letter"
                )
            else:
                result["score"] += 1

            # Check for lowercase letters
            if not re.search(r"[a-z]", password):
                result["issues"].append(
                    "Password must contain at least one lowercase letter"
                )
            else:
                result["score"] += 1

            # Check for digits
            if not re.search(r"\d", password):
                result["issues"].append("Password must contain at least one digit")
            else:
                result["score"] += 1

            # Check for special characters
            if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
                result["issues"].append(
                    "Password must contain at least one special character"
                )
            else:
                result["score"] += 1

            # Check for common patterns
            common_patterns = [r"123456", r"password", r"qwerty", r"abc123"]

            for pattern in common_patterns:
                if re.search(pattern, password.lower()):
                    result["issues"].append("Password contains common patterns")
                    break

            # Determine if password is valid
            result["is_valid"] = len(result["issues"]) == 0 and result["score"] >= 4

        except Exception as e:
            logger.error(f"Error validating password strength: {str(e)}")
            result["issues"].append("Error validating password")

        return result

    @staticmethod
    def sanitize_input(input_string: str, max_length: int = 1000) -> str:
        """
        Sanitize user input to prevent injection attacks.

        Args:
            input_string: Input string to sanitize
            max_length: Maximum allowed length

        Returns:
            Sanitized string
        """
        try:
            if not isinstance(input_string, str):
                return ""

            # Limit length
            sanitized = input_string[:max_length]

            # Remove potentially dangerous characters
            dangerous_chars = ["<", ">", '"', "'", "&", "\x00"]
            for char in dangerous_chars:
                sanitized = sanitized.replace(char, "")

            # Remove SQL injection patterns
            sql_patterns = [
                r"union\s+select",
                r"drop\s+table",
                r"delete\s+from",
                r"insert\s+into",
                r"update\s+set",
                r"--",
                r"/\*",
                r"\*/",
            ]

            for pattern in sql_patterns:
                sanitized = re.sub(pattern, "", sanitized, flags=re.IGNORECASE)

            return sanitized.strip()
        except Exception as e:
            logger.error(f"Error sanitizing input: {str(e)}")
            return ""

    @staticmethod
    def validate_file_upload(
        file_data: bytes, allowed_types: List[str]
    ) -> Dict[str, Any]:
        """
        Validate uploaded file for security.

        Args:
            file_data: File data bytes
            allowed_types: List of allowed MIME types

        Returns:
            Dictionary with validation results
        """
        result = {"is_valid": False, "file_type": None, "issues": []}

        try:
            # Check file size
            max_size = 100 * 1024 * 1024  # 100MB
            if len(file_data) > max_size:
                result["issues"].append(
                    f"File too large (max {max_size // (1024 * 1024)}MB)"
                )
                return result

            if len(file_data) == 0:
                result["issues"].append("Empty file not allowed")
                return result

            # Check file signature (magic bytes)
            file_signatures = {
                "image/jpeg": [b"\xff\xd8\xff"],
                "image/png": [b"\x89\x50\x4e\x47"],
                "image/gif": [b"\x47\x49\x46\x38"],
                "image/bmp": [b"\x42\x4d"],
                "video/mp4": [
                    b"\x00\x00\x00\x18\x66\x74\x79\x70",
                    b"\x00\x00\x00\x20\x66\x74\x79\x70",
                ],
            }

            detected_type = None
            for mime_type, signatures in file_signatures.items():
                for signature in signatures:
                    if file_data.startswith(signature):
                        detected_type = mime_type
                        break
                if detected_type:
                    break

            if not detected_type:
                result["issues"].append("Unknown or invalid file type")
                return result

            result["file_type"] = detected_type

            # Check if detected type is allowed
            if detected_type not in allowed_types:
                result["issues"].append(f"File type not allowed: {detected_type}")
                return result

            # Additional security checks for images
            if detected_type.startswith("image/"):
                # Check for embedded scripts or suspicious content
                suspicious_patterns = [
                    b"<script",
                    b"javascript:",
                    b"<?php",
                    b"<%",
                    b"eval(",
                ]

                for pattern in suspicious_patterns:
                    if pattern in file_data.lower():
                        result["issues"].append("Suspicious content detected in file")
                        return result

            result["is_valid"] = True

        except Exception as e:
            logger.error(f"Error validating file upload: {str(e)}")
            result["issues"].append("Error validating file")

        return result

    @staticmethod
    def generate_csrf_token() -> str:
        """
        Generate CSRF token.

        Returns:
            CSRF token string
        """
        return secrets.token_urlsafe(32)

    @staticmethod
    def validate_csrf_token(token: str, stored_token: str) -> bool:
        """
        Validate CSRF token.

        Args:
            token: Token to validate
            stored_token: Stored token for comparison

        Returns:
            True if valid, False otherwise
        """
        try:
            return secrets.compare_digest(token, stored_token)
        except Exception as e:
            logger.error(f"Error validating CSRF token: {str(e)}")
            return False

    @staticmethod
    def rate_limit_check(
        identifier: str, max_requests: int = 100, time_window: int = 3600
    ) -> Dict[str, Any]:
        """
        Check rate limiting for requests.

        Args:
            identifier: Unique identifier (IP, user ID, etc.)
            max_requests: Maximum requests allowed
            time_window: Time window in seconds

        Returns:
            Dictionary with rate limit status
        """
        # This is a simplified implementation
        # In production, use Redis or similar for distributed rate limiting

        result = {
            "allowed": True,
            "remaining": max_requests,
            "reset_time": datetime.now() + timedelta(seconds=time_window),
        }

        # Implementation would track requests per identifier
        # For now, always allow (placeholder)

        return result

    @staticmethod
    def log_security_event(
        event_type: str, details: Dict[str, Any], severity: str = "INFO"
    ) -> None:
        """
        Log security-related events.

        Args:
            event_type: Type of security event
            details: Event details
            severity: Event severity (INFO, WARNING, ERROR, CRITICAL)
        """
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "event_type": event_type,
                "severity": severity,
                "details": details,
            }

            # Log based on severity
            if severity == "CRITICAL":
                logger.critical(f"Security Event: {log_entry}")
            elif severity == "ERROR":
                logger.error(f"Security Event: {log_entry}")
            elif severity == "WARNING":
                logger.warning(f"Security Event: {log_entry}")
            else:
                logger.info(f"Security Event: {log_entry}")

        except Exception as e:
            logger.error(f"Error logging security event: {str(e)}")

    @staticmethod
    def validate_api_key(api_key: str) -> bool:
        """
        Validate API key format.

        Args:
            api_key: API key to validate

        Returns:
            True if valid format, False otherwise
        """
        try:
            # Check basic format (alphanumeric, specific length)
            if not isinstance(api_key, str):
                return False

            if len(api_key) != 32:  # Expected length
                return False

            if not re.match(r"^[a-zA-Z0-9]+$", api_key):
                return False

            return True
        except Exception:
            return False

    @staticmethod
    def encrypt_sensitive_data(data: str, key: Optional[str] = None) -> Dict[str, str]:
        """
        Encrypt sensitive data (placeholder implementation).

        Args:
            data: Data to encrypt
            key: Encryption key (generated if not provided)

        Returns:
            Dictionary with encrypted data and key
        """
        # This is a placeholder implementation
        # In production, use proper encryption libraries like cryptography

        if key is None:
            key = secrets.token_hex(32)

        # Simple XOR encryption (NOT for production use)
        encrypted = "".join(
            chr(ord(c) ^ ord(key[i % len(key)])) for i, c in enumerate(data)
        )
        encoded = encrypted.encode("latin1").hex()

        return {"encrypted_data": encoded, "key": key}

    @staticmethod
    def decrypt_sensitive_data(encrypted_data: str, key: str) -> str:
        """
        Decrypt sensitive data (placeholder implementation).

        Args:
            encrypted_data: Encrypted data
            key: Decryption key

        Returns:
            Decrypted data
        """
        try:
            # Decode from hex
            decoded = bytes.fromhex(encrypted_data).decode("latin1")

            # Simple XOR decryption (NOT for production use)
            decrypted = "".join(
                chr(ord(c) ^ ord(key[i % len(key)])) for i, c in enumerate(decoded)
            )

            return decrypted
        except Exception as e:
            logger.error(f"Error decrypting data: {str(e)}")
            return ""

"""
Real-Time Object Detection for Smart Cameras - Stream Processor
Developed from Hasif's Workspace

This module handles video stream processing, including drawing detection annotations,
overlays, and visual enhancements for real-time object detection display.
"""

import cv2
import time
import logging
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class StreamProcessor:
    """
    Processes video streams by adding detection annotations, overlays, and visual enhancements.
    """

    def __init__(self, config: Dict):
        """
        Initialize the stream processor.

        Args:
            config: Configuration dictionary containing display settings
        """
        self.config = config

        # Display settings
        self.show_labels = config.get("display", {}).get("show_labels", True)
        self.show_confidence = config.get("display", {}).get("show_confidence", True)
        self.show_fps = config.get("display", {}).get("show_fps", True)
        self.show_timestamp = config.get("display", {}).get("show_timestamp", True)

        # Color settings
        self.colors = self._generate_colors(80)  # COCO has 80 classes
        self.text_color = (255, 255, 255)
        self.background_color = (0, 0, 0)

        # Font settings
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 0.6
        self.font_thickness = 2
        self.line_thickness = 2

        # Performance tracking
        self.frame_count = 0
        self.processing_times = []

        logger.info("StreamProcessor initialized")

    def process_frame(
        self,
        frame: np.ndarray,
        detections: List[Dict],
        fps: float = None,
        additional_info: Dict = None,
    ) -> np.ndarray:
        """
        Process a frame by adding detection annotations and overlays.

        Args:
            frame: Input frame as numpy array
            detections: List of detection dictionaries
            fps: Current FPS for display
            additional_info: Additional information to display

        Returns:
            Processed frame with annotations
        """
        try:
            start_time = time.time()

            # Create a copy of the frame to avoid modifying the original
            processed_frame = frame.copy()

            # Draw detection annotations
            if detections:
                processed_frame = self._draw_detections(processed_frame, detections)

            # Add information overlay
            processed_frame = self._add_info_overlay(
                processed_frame, fps, len(detections), additional_info
            )

            # Add timestamp if enabled
            if self.show_timestamp:
                processed_frame = self._add_timestamp(processed_frame)

            # Track processing time
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)
            if len(self.processing_times) > 100:
                self.processing_times.pop(0)

            self.frame_count += 1

            return processed_frame

        except Exception as e:
            logger.error(f"Error processing frame: {str(e)}")
            return frame

    def _draw_detections(self, frame: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """Draw detection bounding boxes and labels on the frame."""
        try:
            for detection in detections:
                bbox = detection["bbox"]
                class_name = detection["class_name"]
                confidence = detection["confidence"]
                class_id = detection.get("class_id", 0)

                # Get bounding box coordinates
                x = int(bbox["x"])
                y = int(bbox["y"])
                w = int(bbox["width"])
                h = int(bbox["height"])

                # Ensure coordinates are within frame bounds
                x = max(0, min(x, frame.shape[1] - 1))
                y = max(0, min(y, frame.shape[0] - 1))
                w = max(1, min(w, frame.shape[1] - x))
                h = max(1, min(h, frame.shape[0] - y))

                # Get color for this class
                color = self.colors[class_id % len(self.colors)]

                # Draw bounding box
                cv2.rectangle(frame, (x, y), (x + w, y + h), color, self.line_thickness)

                # Prepare label text
                if self.show_labels and self.show_confidence:
                    label = f"{class_name}: {confidence:.2f}"
                elif self.show_labels:
                    label = class_name
                elif self.show_confidence:
                    label = f"{confidence:.2f}"
                else:
                    label = ""

                # Draw label background and text
                if label:
                    self._draw_label(frame, label, (x, y), color)

            return frame

        except Exception as e:
            logger.error(f"Error drawing detections: {str(e)}")
            return frame

    def _draw_label(
        self,
        frame: np.ndarray,
        text: str,
        position: Tuple[int, int],
        color: Tuple[int, int, int],
    ):
        """Draw a label with background at the specified position."""
        try:
            x, y = position

            # Get text size
            (text_width, text_height), baseline = cv2.getTextSize(
                text, self.font, self.font_scale, self.font_thickness
            )

            # Calculate label background coordinates
            label_y = (
                y - text_height - 10
                if y - text_height - 10 > 0
                else y + text_height + 10
            )

            # Draw label background
            cv2.rectangle(
                frame,
                (x, label_y - text_height - 5),
                (x + text_width + 10, label_y + 5),
                color,
                -1,
            )

            # Draw text
            cv2.putText(
                frame,
                text,
                (x + 5, label_y - 5),
                self.font,
                self.font_scale,
                self.text_color,
                self.font_thickness,
            )

        except Exception as e:
            logger.error(f"Error drawing label: {str(e)}")

    def _add_info_overlay(
        self,
        frame: np.ndarray,
        fps: float = None,
        detection_count: int = 0,
        additional_info: Dict = None,
    ) -> np.ndarray:
        """Add information overlay to the frame."""
        try:
            # Create semi-transparent overlay
            overlay = frame.copy()
            overlay_height = 120
            cv2.rectangle(
                overlay, (10, 10), (400, overlay_height), self.background_color, -1
            )
            cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)

            # Add text information
            y_offset = 35
            line_spacing = 25

            # FPS
            if fps is not None and self.show_fps:
                fps_text = f"FPS: {fps:.1f}"
                cv2.putText(
                    frame,
                    fps_text,
                    (20, y_offset),
                    self.font,
                    self.font_scale,
                    self.text_color,
                    self.font_thickness,
                )
                y_offset += line_spacing

            # Detection count
            detection_text = f"Detections: {detection_count}"
            cv2.putText(
                frame,
                detection_text,
                (20, y_offset),
                self.font,
                self.font_scale,
                self.text_color,
                self.font_thickness,
            )
            y_offset += line_spacing

            # Frame count
            frame_text = f"Frame: {self.frame_count}"
            cv2.putText(
                frame,
                frame_text,
                (20, y_offset),
                self.font,
                self.font_scale,
                self.text_color,
                self.font_thickness,
            )
            y_offset += line_spacing

            # Additional information
            if additional_info:
                for key, value in additional_info.items():
                    if y_offset < overlay_height - 10:
                        info_text = f"{key}: {value}"
                        cv2.putText(
                            frame,
                            info_text,
                            (20, y_offset),
                            self.font,
                            self.font_scale,
                            self.text_color,
                            self.font_thickness,
                        )
                        y_offset += line_spacing

            return frame

        except Exception as e:
            logger.error(f"Error adding info overlay: {str(e)}")
            return frame

    def _add_timestamp(self, frame: np.ndarray) -> np.ndarray:
        """Add timestamp to the frame."""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Get text size
            (text_width, text_height), _ = cv2.getTextSize(
                timestamp, self.font, self.font_scale, self.font_thickness
            )

            # Position at bottom right
            x = frame.shape[1] - text_width - 20
            y = frame.shape[0] - 20

            # Draw background
            cv2.rectangle(
                frame,
                (x - 10, y - text_height - 10),
                (x + text_width + 10, y + 10),
                self.background_color,
                -1,
            )

            # Draw timestamp
            cv2.putText(
                frame,
                timestamp,
                (x, y),
                self.font,
                self.font_scale,
                self.text_color,
                self.font_thickness,
            )

            return frame

        except Exception as e:
            logger.error(f"Error adding timestamp: {str(e)}")
            return frame

    def _generate_colors(self, num_classes: int) -> List[Tuple[int, int, int]]:
        """Generate distinct colors for different classes."""
        colors = []

        # Use HSV color space for better color distribution
        for i in range(num_classes):
            hue = int(180 * i / num_classes)
            saturation = 255
            value = 255

            # Convert HSV to BGR
            hsv_color = np.uint8([[[hue, saturation, value]]])
            bgr_color = cv2.cvtColor(hsv_color, cv2.COLOR_HSV2BGR)[0][0]
            colors.append(tuple(map(int, bgr_color)))

        return colors

    def add_detection_trail(
        self,
        frame: np.ndarray,
        detection_history: List[List[Dict]],
        trail_length: int = 10,
    ) -> np.ndarray:
        """Add detection trails to show object movement."""
        try:
            if len(detection_history) < 2:
                return frame

            # Group detections by class and track centers
            class_trails = {}

            for frame_detections in detection_history[-trail_length:]:
                for detection in frame_detections:
                    class_name = detection["class_name"]
                    bbox = detection["bbox"]

                    # Calculate center point
                    center_x = int(bbox["x"] + bbox["width"] / 2)
                    center_y = int(bbox["y"] + bbox["height"] / 2)

                    if class_name not in class_trails:
                        class_trails[class_name] = []

                    class_trails[class_name].append((center_x, center_y))

            # Draw trails
            for class_name, trail in class_trails.items():
                if len(trail) > 1:
                    # Get class color
                    class_id = hash(class_name) % len(self.colors)
                    color = self.colors[class_id]

                    # Draw trail lines
                    for i in range(1, len(trail)):
                        alpha = i / len(trail)  # Fade effect
                        thickness = max(1, int(self.line_thickness * alpha))

                        cv2.line(frame, trail[i - 1], trail[i], color, thickness)

            return frame

        except Exception as e:
            logger.error(f"Error adding detection trail: {str(e)}")
            return frame

    def add_detection_heatmap(
        self, frame: np.ndarray, detection_heatmap: np.ndarray, alpha: float = 0.3
    ) -> np.ndarray:
        """Add detection heatmap overlay."""
        try:
            if (
                detection_heatmap is None
                or detection_heatmap.shape[:2] != frame.shape[:2]
            ):
                return frame

            # Normalize heatmap
            normalized_heatmap = cv2.normalize(
                detection_heatmap, None, 0, 255, cv2.NORM_MINMAX
            )

            # Apply colormap
            colored_heatmap = cv2.applyColorMap(
                normalized_heatmap.astype(np.uint8), cv2.COLORMAP_JET
            )

            # Blend with original frame
            blended = cv2.addWeighted(frame, 1 - alpha, colored_heatmap, alpha, 0)

            return blended

        except Exception as e:
            logger.error(f"Error adding detection heatmap: {str(e)}")
            return frame

    def add_roi_overlay(
        self,
        frame: np.ndarray,
        roi_polygons: List[List[Tuple[int, int]]],
        roi_labels: List[str] = None,
    ) -> np.ndarray:
        """Add Region of Interest (ROI) overlays."""
        try:
            overlay = frame.copy()

            for i, polygon in enumerate(roi_polygons):
                if len(polygon) < 3:
                    continue

                # Convert to numpy array
                pts = np.array(polygon, np.int32)
                pts = pts.reshape((-1, 1, 2))

                # Draw filled polygon with transparency
                color = self.colors[i % len(self.colors)]
                cv2.fillPoly(overlay, [pts], color)

                # Draw polygon outline
                cv2.polylines(frame, [pts], True, color, self.line_thickness)

                # Add label if provided
                if roi_labels and i < len(roi_labels):
                    # Calculate centroid for label placement
                    centroid_x = int(np.mean([pt[0] for pt in polygon]))
                    centroid_y = int(np.mean([pt[1] for pt in polygon]))

                    self._draw_label(
                        frame, roi_labels[i], (centroid_x, centroid_y), color
                    )

            # Blend overlay with original frame
            cv2.addWeighted(overlay, 0.2, frame, 0.8, 0, frame)

            return frame

        except Exception as e:
            logger.error(f"Error adding ROI overlay: {str(e)}")
            return frame

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get stream processing performance statistics."""
        if not self.processing_times:
            return {
                "average_processing_time": 0.0,
                "max_processing_time": 0.0,
                "min_processing_time": 0.0,
                "frames_processed": self.frame_count,
            }

        return {
            "average_processing_time": np.mean(self.processing_times),
            "max_processing_time": np.max(self.processing_times),
            "min_processing_time": np.min(self.processing_times),
            "frames_processed": self.frame_count,
            "processing_fps": 1.0 / np.mean(self.processing_times)
            if np.mean(self.processing_times) > 0
            else 0,
        }

    def reset_stats(self):
        """Reset processing statistics."""
        self.frame_count = 0
        self.processing_times = []
        logger.info("Stream processing statistics reset")

    def update_display_settings(self, settings: Dict[str, Any]):
        """Update display settings."""
        if "show_labels" in settings:
            self.show_labels = settings["show_labels"]
        if "show_confidence" in settings:
            self.show_confidence = settings["show_confidence"]
        if "show_fps" in settings:
            self.show_fps = settings["show_fps"]
        if "show_timestamp" in settings:
            self.show_timestamp = settings["show_timestamp"]
        if "font_scale" in settings:
            self.font_scale = settings["font_scale"]
        if "line_thickness" in settings:
            self.line_thickness = settings["line_thickness"]

        logger.info(f"Display settings updated: {settings}")

"""
Real-Time Object Detection for Smart Cameras - Validation Utilities
Developed from Hasif's Workspace

This module contains validation utilities for input data, configurations, and system parameters.
"""

import re
import logging
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Custom exception for validation errors."""

    pass


class Validators:
    """
    Collection of validation utilities for the real-time object detection system.
    """

    @staticmethod
    def validate_camera_index(camera_index: Any) -> bool:
        """
        Validate camera index.

        Args:
            camera_index: Camera index to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            if not isinstance(camera_index, int):
                return False
            return 0 <= camera_index <= 10  # Reasonable range for camera indices
        except Exception:
            return False

    @staticmethod
    def validate_confidence_threshold(threshold: Any) -> bool:
        """
        Validate confidence threshold.

        Args:
            threshold: Threshold value to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            if not isinstance(threshold, (int, float)):
                return False
            return 0.0 <= threshold <= 1.0
        except Exception:
            return False

    @staticmethod
    def validate_nms_threshold(threshold: Any) -> bool:
        """
        Validate NMS threshold.

        Args:
            threshold: Threshold value to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            if not isinstance(threshold, (int, float)):
                return False
            return 0.0 <= threshold <= 1.0
        except Exception:
            return False

    @staticmethod
    def validate_model_path(model_path: Any) -> bool:
        """
        Validate model file path.

        Args:
            model_path: Model path to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            if not isinstance(model_path, (str, Path)):
                return False

            path = Path(model_path)

            # Check if file exists
            if not path.exists():
                return False

            # Check file extension
            valid_extensions = [".pt", ".pth", ".onnx", ".trt", ".pb", ".h5"]
            if path.suffix.lower() not in valid_extensions:
                return False

            # Check file size (should be > 1MB for valid models)
            if path.stat().st_size < 1024 * 1024:
                return False

            return True
        except Exception:
            return False

    @staticmethod
    def validate_image_size(size: Any) -> bool:
        """
        Validate image size.

        Args:
            size: Size to validate (width, height)

        Returns:
            True if valid, False otherwise
        """
        try:
            if not isinstance(size, (list, tuple)) or len(size) != 2:
                return False

            width, height = size

            if not isinstance(width, int) or not isinstance(height, int):
                return False

            # Reasonable size limits
            return 32 <= width <= 4096 and 32 <= height <= 4096
        except Exception:
            return False

    @staticmethod
    def validate_fps(fps: Any) -> bool:
        """
        Validate FPS value.

        Args:
            fps: FPS value to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            if not isinstance(fps, (int, float)):
                return False
            return 1 <= fps <= 120  # Reasonable FPS range
        except Exception:
            return False

    @staticmethod
    def validate_session_name(name: Any) -> bool:
        """
        Validate session name.

        Args:
            name: Session name to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            if not isinstance(name, str):
                return False

            # Check length
            if not (1 <= len(name) <= 100):
                return False

            # Check for valid characters (alphanumeric, spaces, hyphens, underscores)
            pattern = r"^[a-zA-Z0-9\s\-_]+$"
            return bool(re.match(pattern, name))
        except Exception:
            return False

    @staticmethod
    def validate_detection_config(config: Dict[str, Any]) -> List[str]:
        """
        Validate detection configuration.

        Args:
            config: Configuration dictionary to validate

        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []

        try:
            # Validate confidence threshold
            if "confidence_threshold" in config:
                if not Validators.validate_confidence_threshold(
                    config["confidence_threshold"]
                ):
                    errors.append(
                        "Invalid confidence threshold (must be between 0.0 and 1.0)"
                    )

            # Validate NMS threshold
            if "nms_threshold" in config:
                if not Validators.validate_nms_threshold(config["nms_threshold"]):
                    errors.append("Invalid NMS threshold (must be between 0.0 and 1.0)")

            # Validate camera index
            if "camera_index" in config:
                if not Validators.validate_camera_index(config["camera_index"]):
                    errors.append(
                        "Invalid camera index (must be integer between 0 and 10)"
                    )

            # Validate model name
            if "model_name" in config:
                if (
                    not isinstance(config["model_name"], str)
                    or not config["model_name"].strip()
                ):
                    errors.append("Invalid model name (must be non-empty string)")

            # Validate input size
            if "input_size" in config:
                if not Validators.validate_image_size(config["input_size"]):
                    errors.append(
                        "Invalid input size (must be [width, height] with values 32-4096)"
                    )

        except Exception as e:
            errors.append(f"Error validating configuration: {str(e)}")

        return errors

    @staticmethod
    def validate_file_upload(file_data: Dict[str, Any]) -> List[str]:
        """
        Validate file upload data.

        Args:
            file_data: File upload data to validate

        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []

        try:
            # Check if file data exists
            if not file_data:
                errors.append("No file data provided")
                return errors

            # Validate filename
            filename = file_data.get("filename", "")
            if not filename:
                errors.append("No filename provided")
            else:
                # Check file extension
                valid_extensions = [
                    ".jpg",
                    ".jpeg",
                    ".png",
                    ".bmp",
                    ".tiff",
                    ".mp4",
                    ".avi",
                    ".mov",
                ]
                if not any(filename.lower().endswith(ext) for ext in valid_extensions):
                    errors.append(
                        f"Invalid file type. Supported: {', '.join(valid_extensions)}"
                    )

            # Validate file size
            file_size = file_data.get("size", 0)
            max_size = 100 * 1024 * 1024  # 100MB
            if file_size > max_size:
                errors.append(
                    f"File too large. Maximum size: {max_size // (1024 * 1024)}MB"
                )

            if file_size == 0:
                errors.append("Empty file not allowed")

        except Exception as e:
            errors.append(f"Error validating file upload: {str(e)}")

        return errors

    @staticmethod
    def validate_api_request(
        request_data: Dict[str, Any], required_fields: List[str]
    ) -> List[str]:
        """
        Validate API request data.

        Args:
            request_data: Request data to validate
            required_fields: List of required field names

        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []

        try:
            # Check for required fields
            for field in required_fields:
                if field not in request_data:
                    errors.append(f"Missing required field: {field}")
                elif request_data[field] is None:
                    errors.append(f"Field cannot be null: {field}")
                elif (
                    isinstance(request_data[field], str)
                    and not request_data[field].strip()
                ):
                    errors.append(f"Field cannot be empty: {field}")

        except Exception as e:
            errors.append(f"Error validating API request: {str(e)}")

        return errors

    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        Sanitize filename for safe storage.

        Args:
            filename: Original filename

        Returns:
            Sanitized filename
        """
        try:
            # Remove path components
            filename = Path(filename).name

            # Replace invalid characters
            invalid_chars = r'[<>:"/\\|?*]'
            sanitized = re.sub(invalid_chars, "_", filename)

            # Remove multiple underscores
            sanitized = re.sub(r"_+", "_", sanitized)

            # Limit length
            if len(sanitized) > 255:
                name, ext = Path(sanitized).stem, Path(sanitized).suffix
                max_name_length = 255 - len(ext)
                sanitized = name[:max_name_length] + ext

            return sanitized
        except Exception:
            return "unknown_file"

    @staticmethod
    def validate_bbox(
        bbox: Dict[str, Any], image_width: int, image_height: int
    ) -> bool:
        """
        Validate bounding box coordinates.

        Args:
            bbox: Bounding box dictionary with x, y, width, height
            image_width: Image width for bounds checking
            image_height: Image height for bounds checking

        Returns:
            True if valid, False otherwise
        """
        try:
            required_keys = ["x", "y", "width", "height"]
            if not all(key in bbox for key in required_keys):
                return False

            x, y, width, height = bbox["x"], bbox["y"], bbox["width"], bbox["height"]

            # Check types
            if not all(isinstance(val, (int, float)) for val in [x, y, width, height]):
                return False

            # Check bounds
            if x < 0 or y < 0 or width <= 0 or height <= 0:
                return False

            if x + width > image_width or y + height > image_height:
                return False

            return True
        except Exception:
            return False

    @staticmethod
    def validate_detection_result(detection: Dict[str, Any]) -> bool:
        """
        Validate detection result format.

        Args:
            detection: Detection result dictionary

        Returns:
            True if valid, False otherwise
        """
        try:
            required_keys = ["class_name", "confidence", "bbox"]
            if not all(key in detection for key in required_keys):
                return False

            # Validate class name
            if (
                not isinstance(detection["class_name"], str)
                or not detection["class_name"].strip()
            ):
                return False

            # Validate confidence
            if not Validators.validate_confidence_threshold(detection["confidence"]):
                return False

            # Validate bbox (assuming reasonable image size)
            if not isinstance(detection["bbox"], dict):
                return False

            return True
        except Exception:
            return False

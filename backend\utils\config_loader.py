"""
Real-Time Object Detection for Smart Cameras - Configuration Loader
Developed from Hasif's Workspace

This module handles loading and parsing of YAML configuration files
for the real-time object detection system.
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class ConfigLoader:
    """
    Utility class for loading and managing configuration files.
    """

    @staticmethod
    def load_config(config_path: str) -> Dict[str, Any]:
        """
        Load configuration from YAML file.

        Args:
            config_path: Path to the configuration file

        Returns:
            Configuration dictionary
        """
        try:
            config_file = Path(config_path)

            if not config_file.exists():
                logger.error(f"Configuration file not found: {config_path}")
                return ConfigLoader._get_default_config()

            with open(config_file, "r", encoding="utf-8") as file:
                config = yaml.safe_load(file)

            # Validate and merge with defaults
            config = ConfigLoader._validate_config(config)

            logger.info(f"Configuration loaded successfully from: {config_path}")
            return config

        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML configuration: {str(e)}")
            return ConfigLoader._get_default_config()
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            return ConfigLoader._get_default_config()

    @staticmethod
    def save_config(config: Dict[str, Any], config_path: str) -> bool:
        """
        Save configuration to YAML file.

        Args:
            config: Configuration dictionary to save
            config_path: Path to save the configuration file

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            config_file = Path(config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)

            with open(config_file, "w", encoding="utf-8") as file:
                yaml.dump(config, file, default_flow_style=False, indent=2)

            logger.info(f"Configuration saved successfully to: {config_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving configuration: {str(e)}")
            return False

    @staticmethod
    def _validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate configuration and merge with defaults.

        Args:
            config: Configuration dictionary to validate

        Returns:
            Validated configuration dictionary
        """
        default_config = ConfigLoader._get_default_config()

        # Merge with defaults (config takes precedence)
        validated_config = ConfigLoader._deep_merge(default_config, config)

        # Validate specific sections
        validated_config = ConfigLoader._validate_models_section(validated_config)
        validated_config = ConfigLoader._validate_camera_section(validated_config)
        validated_config = ConfigLoader._validate_detection_section(validated_config)

        return validated_config

    @staticmethod
    def _validate_models_section(config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate models section of configuration."""
        if "models" not in config:
            config["models"] = {}

        for model_name, model_config in config["models"].items():
            # Ensure required fields
            if "path" not in model_config:
                logger.warning(f"Model {model_name} missing 'path' field")
                model_config["path"] = f"models/{model_name}.pt"

            if "input_size" not in model_config:
                model_config["input_size"] = [640, 640]

            if "confidence_threshold" not in model_config:
                model_config["confidence_threshold"] = 0.5

            if "nms_threshold" not in model_config:
                model_config["nms_threshold"] = 0.4

        return config

    @staticmethod
    def _validate_camera_section(config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate camera section of configuration."""
        if "camera" not in config:
            config["camera"] = {}

        camera_config = config["camera"]

        # Set defaults
        camera_config.setdefault("default_index", 0)
        camera_config.setdefault("resolution", [640, 480])
        camera_config.setdefault("fps", 30)
        camera_config.setdefault("buffer_size", 1)

        # Validate resolution
        if (
            not isinstance(camera_config["resolution"], list)
            or len(camera_config["resolution"]) != 2
        ):
            logger.warning("Invalid camera resolution, using default [640, 480]")
            camera_config["resolution"] = [640, 480]

        # Validate FPS
        if (
            not isinstance(camera_config["fps"], (int, float))
            or camera_config["fps"] <= 0
        ):
            logger.warning("Invalid camera FPS, using default 30")
            camera_config["fps"] = 30

        return config

    @staticmethod
    def _validate_detection_section(config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate detection section of configuration."""
        if "detection" not in config:
            config["detection"] = {}

        detection_config = config["detection"]

        # Set defaults
        detection_config.setdefault("confidence_threshold", 0.5)
        detection_config.setdefault("nms_threshold", 0.4)
        detection_config.setdefault("max_detections", 100)
        detection_config.setdefault("use_gpu", True)

        # Validate thresholds
        if not 0 <= detection_config["confidence_threshold"] <= 1:
            logger.warning("Invalid confidence threshold, using default 0.5")
            detection_config["confidence_threshold"] = 0.5

        if not 0 <= detection_config["nms_threshold"] <= 1:
            logger.warning("Invalid NMS threshold, using default 0.4")
            detection_config["nms_threshold"] = 0.4

        return config

    @staticmethod
    def _deep_merge(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deep merge two dictionaries.

        Args:
            dict1: Base dictionary
            dict2: Dictionary to merge (takes precedence)

        Returns:
            Merged dictionary
        """
        result = dict1.copy()

        for key, value in dict2.items():
            if (
                key in result
                and isinstance(result[key], dict)
                and isinstance(value, dict)
            ):
                result[key] = ConfigLoader._deep_merge(result[key], value)
            else:
                result[key] = value

        return result

    @staticmethod
    def _get_default_config() -> Dict[str, Any]:
        """
        Get default configuration.

        Returns:
            Default configuration dictionary
        """
        return {
            "models": {
                "yolov8n": {
                    "path": "models/yolo/yolov8n.pt",
                    "display_name": "YOLOv8 Nano",
                    "description": "Fastest model, optimized for real-time applications",
                    "input_size": [640, 640],
                    "classes": 80,
                    "confidence_threshold": 0.5,
                    "nms_threshold": 0.4,
                }
            },
            "camera": {
                "default_index": 0,
                "resolution": [640, 480],
                "fps": 30,
                "buffer_size": 1,
            },
            "detection": {
                "confidence_threshold": 0.5,
                "nms_threshold": 0.4,
                "max_detections": 100,
                "use_gpu": True,
                "batch_processing": False,
                "optimization": "none",
            },
            "display": {
                "show_labels": True,
                "show_confidence": True,
                "show_fps": True,
                "show_timestamp": True,
                "font_scale": 0.6,
                "font_thickness": 2,
                "line_thickness": 2,
            },
            "output": {
                "save_detections": False,
                "save_video": False,
                "save_images": False,
                "video_output_path": "data/output/videos",
                "image_output_path": "data/output/images",
            },
            "performance": {
                "enable_monitoring": True,
                "log_interval": 10,
                "metrics_retention": 1000,
            },
        }

    @staticmethod
    def get_model_config(
        config: Dict[str, Any], model_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get configuration for a specific model.

        Args:
            config: Main configuration dictionary
            model_name: Name of the model

        Returns:
            Model configuration dictionary or None if not found
        """
        models = config.get("models", {})
        return models.get(model_name)

    @staticmethod
    def update_model_config(
        config: Dict[str, Any], model_name: str, model_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update configuration for a specific model.

        Args:
            config: Main configuration dictionary
            model_name: Name of the model
            model_config: New model configuration

        Returns:
            Updated configuration dictionary
        """
        if "models" not in config:
            config["models"] = {}

        config["models"][model_name] = model_config
        return config

    @staticmethod
    def validate_model_path(model_path: str) -> bool:
        """
        Validate that a model file exists.

        Args:
            model_path: Path to the model file

        Returns:
            True if model file exists, False otherwise
        """
        try:
            return Path(model_path).exists()
        except Exception:
            return False

    @staticmethod
    def get_available_models(models_dir: str = "models") -> Dict[str, str]:
        """
        Get available model files from the models directory.

        Args:
            models_dir: Directory containing model files

        Returns:
            Dictionary mapping model names to file paths
        """
        available_models = {}

        try:
            models_path = Path(models_dir)
            if not models_path.exists():
                return available_models

            # Search for model files
            model_extensions = [".pt", ".onnx", ".trt", ".pb"]

            for ext in model_extensions:
                for model_file in models_path.rglob(f"*{ext}"):
                    model_name = model_file.stem
                    available_models[model_name] = str(model_file)

            logger.info(f"Found {len(available_models)} available models")

        except Exception as e:
            logger.error(f"Error scanning for available models: {str(e)}")

        return available_models

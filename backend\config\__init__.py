"""
Real-Time Object Detection for Smart Cameras - Configuration Package
Developed from Hasif's Workspace

This package contains configuration classes and settings for the
real-time object detection system.
"""

from .config import (
    Config,
    DevelopmentConfig,
    TestingConfig,
    ProductionConfig,
    DockerConfig,
    get_config,
)

__all__ = [
    "Config",
    "DevelopmentConfig",
    "TestingConfig",
    "ProductionConfig",
    "DockerConfig",
    "get_config",
]

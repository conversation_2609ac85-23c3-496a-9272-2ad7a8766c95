"""
Real-Time Object Detection for Smart Cameras - Camera Manager
Developed from Hasif's Workspace

This module handles camera initialization, frame capture, and video stream management
for real-time object detection applications.
"""

import cv2
import time
import logging
import threading
from typing import Optional, Tuple, Dict, Any, List
import numpy as np

logger = logging.getLogger(__name__)


class CameraManager:
    """
    Manages camera operations including initialization, frame capture, and stream handling.
    """

    def __init__(self, config: Dict):
        """
        Initialize the camera manager.

        Args:
            config: Configuration dictionary containing camera settings
        """
        self.config = config
        self.camera = None
        self.current_camera_index = None
        self.is_streaming = False
        self.frame_buffer = None
        self.buffer_lock = threading.Lock()

        # Camera properties
        self.width = config.get("camera", {}).get("width", 640)
        self.height = config.get("camera", {}).get("height", 480)
        self.fps = config.get("camera", {}).get("fps", 30)
        self.buffer_size = config.get("camera", {}).get("buffer_size", 1)

        # Performance tracking
        self.frame_count = 0
        self.dropped_frames = 0
        self.last_frame_time = 0

        logger.info("CameraManager initialized")

    def initialize_camera(self, camera_index: int = 0) -> bool:
        """
        Initialize camera with specified index.

        Args:
            camera_index: Camera device index (0 for default camera)

        Returns:
            True if camera initialized successfully, False otherwise
        """
        try:
            # Release existing camera if any
            if self.camera is not None:
                self.release_camera()

            # Try to open camera
            self.camera = cv2.VideoCapture(camera_index)

            if not self.camera.isOpened():
                logger.error(f"Failed to open camera with index {camera_index}")
                return False

            # Set camera properties
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
            self.camera.set(cv2.CAP_PROP_FPS, self.fps)
            self.camera.set(cv2.CAP_PROP_BUFFERSIZE, self.buffer_size)

            # Verify camera properties
            actual_width = int(self.camera.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(self.camera.get(cv2.CAP_PROP_FRAME_HEIGHT))
            actual_fps = self.camera.get(cv2.CAP_PROP_FPS)

            self.current_camera_index = camera_index

            logger.info(f"Camera {camera_index} initialized successfully")
            logger.info(
                f"Resolution: {actual_width}x{actual_height}, FPS: {actual_fps}"
            )

            # Test frame capture
            ret, test_frame = self.camera.read()
            if not ret or test_frame is None:
                logger.error("Failed to capture test frame")
                self.release_camera()
                return False

            logger.info(f"Test frame captured: {test_frame.shape}")
            return True

        except Exception as e:
            logger.error(f"Error initializing camera {camera_index}: {str(e)}")
            return False

    def get_frame(self) -> Optional[np.ndarray]:
        """
        Capture a frame from the camera.

        Returns:
            Frame as numpy array or None if capture failed
        """
        if self.camera is None or not self.camera.isOpened():
            logger.warning("Camera not initialized or not opened")
            return None

        try:
            ret, frame = self.camera.read()

            if not ret or frame is None:
                logger.warning("Failed to capture frame")
                self.dropped_frames += 1
                return None

            # Update frame statistics
            self.frame_count += 1
            current_time = time.time()

            if self.last_frame_time > 0:
                frame_interval = current_time - self.last_frame_time
                actual_fps = 1.0 / frame_interval if frame_interval > 0 else 0

                # Log FPS periodically
                if self.frame_count % 100 == 0:
                    logger.debug(
                        f"Actual FPS: {actual_fps:.1f}, Dropped frames: {self.dropped_frames}"
                    )

            self.last_frame_time = current_time

            return frame

        except Exception as e:
            logger.error(f"Error capturing frame: {str(e)}")
            return None

    def start_streaming(self) -> bool:
        """
        Start continuous frame streaming in a separate thread.

        Returns:
            True if streaming started successfully, False otherwise
        """
        if self.is_streaming:
            logger.warning("Streaming is already active")
            return False

        if self.camera is None or not self.camera.isOpened():
            logger.error("Camera not initialized")
            return False

        try:
            self.is_streaming = True
            self.streaming_thread = threading.Thread(
                target=self._streaming_loop, daemon=True
            )
            self.streaming_thread.start()

            logger.info("Camera streaming started")
            return True

        except Exception as e:
            logger.error(f"Error starting streaming: {str(e)}")
            self.is_streaming = False
            return False

    def stop_streaming(self):
        """Stop continuous frame streaming."""
        if not self.is_streaming:
            return

        self.is_streaming = False

        # Wait for streaming thread to finish
        if hasattr(self, "streaming_thread") and self.streaming_thread.is_alive():
            self.streaming_thread.join(timeout=2.0)

        logger.info("Camera streaming stopped")

    def _streaming_loop(self):
        """Internal streaming loop running in separate thread."""
        try:
            while self.is_streaming:
                frame = self.get_frame()

                if frame is not None:
                    with self.buffer_lock:
                        self.frame_buffer = frame.copy()

                # Small delay to prevent excessive CPU usage
                time.sleep(0.001)

        except Exception as e:
            logger.error(f"Error in streaming loop: {str(e)}")
        finally:
            self.is_streaming = False

    def get_buffered_frame(self) -> Optional[np.ndarray]:
        """
        Get the latest frame from the buffer (for streaming mode).

        Returns:
            Latest frame or None if no frame available
        """
        with self.buffer_lock:
            if self.frame_buffer is not None:
                return self.frame_buffer.copy()
            return None

    def get_camera_info(self) -> Dict[str, Any]:
        """
        Get camera information and properties.

        Returns:
            Dictionary containing camera information
        """
        if self.camera is None:
            return {"status": "not_initialized"}

        try:
            info = {
                "status": "active" if self.camera.isOpened() else "inactive",
                "camera_index": self.current_camera_index,
                "width": int(self.camera.get(cv2.CAP_PROP_FRAME_WIDTH)),
                "height": int(self.camera.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                "fps": self.camera.get(cv2.CAP_PROP_FPS),
                "backend": self.camera.getBackendName(),
                "frame_count": self.frame_count,
                "dropped_frames": self.dropped_frames,
                "is_streaming": self.is_streaming,
            }

            # Add codec information if available
            try:
                fourcc = int(self.camera.get(cv2.CAP_PROP_FOURCC))
                codec = "".join([chr((fourcc >> 8 * i) & 0xFF) for i in range(4)])
                info["codec"] = codec
            except:
                info["codec"] = "unknown"

            return info

        except Exception as e:
            logger.error(f"Error getting camera info: {str(e)}")
            return {"status": "error", "message": str(e)}

    def set_camera_properties(self, properties: Dict[str, Any]) -> bool:
        """
        Set camera properties.

        Args:
            properties: Dictionary of properties to set

        Returns:
            True if properties set successfully, False otherwise
        """
        if self.camera is None or not self.camera.isOpened():
            logger.error("Camera not initialized")
            return False

        try:
            success = True

            for prop_name, value in properties.items():
                if prop_name == "width":
                    result = self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, value)
                elif prop_name == "height":
                    result = self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, value)
                elif prop_name == "fps":
                    result = self.camera.set(cv2.CAP_PROP_FPS, value)
                elif prop_name == "brightness":
                    result = self.camera.set(cv2.CAP_PROP_BRIGHTNESS, value)
                elif prop_name == "contrast":
                    result = self.camera.set(cv2.CAP_PROP_CONTRAST, value)
                elif prop_name == "saturation":
                    result = self.camera.set(cv2.CAP_PROP_SATURATION, value)
                elif prop_name == "exposure":
                    result = self.camera.set(cv2.CAP_PROP_EXPOSURE, value)
                else:
                    logger.warning(f"Unknown camera property: {prop_name}")
                    continue

                if not result:
                    logger.warning(
                        f"Failed to set camera property {prop_name} to {value}"
                    )
                    success = False
                else:
                    logger.info(f"Set camera property {prop_name} to {value}")

            return success

        except Exception as e:
            logger.error(f"Error setting camera properties: {str(e)}")
            return False

    def capture_image(self, filename: str) -> bool:
        """
        Capture and save a single image.

        Args:
            filename: Path to save the captured image

        Returns:
            True if image captured and saved successfully, False otherwise
        """
        try:
            frame = self.get_frame()
            if frame is None:
                logger.error("Failed to capture frame for image")
                return False

            success = cv2.imwrite(filename, frame)
            if success:
                logger.info(f"Image saved to: {filename}")
            else:
                logger.error(f"Failed to save image to: {filename}")

            return success

        except Exception as e:
            logger.error(f"Error capturing image: {str(e)}")
            return False

    def release_camera(self):
        """Release camera resources."""
        try:
            # Stop streaming if active
            if self.is_streaming:
                self.stop_streaming()

            # Release camera
            if self.camera is not None:
                self.camera.release()
                self.camera = None

            # Reset state
            self.current_camera_index = None
            self.frame_count = 0
            self.dropped_frames = 0
            self.last_frame_time = 0

            with self.buffer_lock:
                self.frame_buffer = None

            logger.info("Camera released successfully")

        except Exception as e:
            logger.error(f"Error releasing camera: {str(e)}")

    def list_available_cameras(self) -> List[int]:
        """
        List available camera indices.

        Returns:
            List of available camera indices
        """
        available_cameras = []

        try:
            # Test camera indices 0-10
            for i in range(10):
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    ret, _ = cap.read()
                    if ret:
                        available_cameras.append(i)
                        logger.info(f"Camera {i} is available")
                cap.release()

            logger.info(
                f"Found {len(available_cameras)} available cameras: {available_cameras}"
            )
            return available_cameras

        except Exception as e:
            logger.error(f"Error listing cameras: {str(e)}")
            return []

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get camera performance statistics.

        Returns:
            Dictionary containing performance statistics
        """
        if self.last_frame_time > 0 and self.frame_count > 1:
            total_time = time.time() - (
                self.last_frame_time - (self.frame_count - 1) / self.fps
            )
            actual_fps = self.frame_count / total_time if total_time > 0 else 0
        else:
            actual_fps = 0

        drop_rate = (
            self.dropped_frames / max(self.frame_count + self.dropped_frames, 1)
        ) * 100

        return {
            "frame_count": self.frame_count,
            "dropped_frames": self.dropped_frames,
            "drop_rate_percent": drop_rate,
            "actual_fps": actual_fps,
            "target_fps": self.fps,
            "is_streaming": self.is_streaming,
            "camera_index": self.current_camera_index,
        }

    def __del__(self):
        """Destructor to ensure camera is released."""
        self.release_camera()
